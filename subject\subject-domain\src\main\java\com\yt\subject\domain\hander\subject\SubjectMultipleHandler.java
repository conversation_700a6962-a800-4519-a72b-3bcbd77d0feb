package com.yt.subject.domain.hander.subject;

import com.alibaba.fastjson.JSON;
import com.yt.subject.commom.enums.IsDeletedFlagEnum;
import com.yt.subject.commom.enums.SubjectInfoEnums;
import com.yt.subject.domain.convert.MultipleConverter;
import com.yt.subject.domain.entity.SubjectAnswerBo;
import com.yt.subject.domain.entity.SubjectInfoBo;
import com.yt.subject.domain.entity.SubjectOptionBo;
import com.yt.subject.infra.basic.entity.SubjectMultiple;
import com.yt.subject.infra.basic.service.SubjectMultipleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.List;

@Component
@Slf4j
public class SubjectMultipleHandler implements SubjectTypeHandler {

    @Resource
    private SubjectMultipleService subjectMultipleService;

    @Override
    public SubjectInfoEnums getSubjectType() {
        return SubjectInfoEnums.MULTIPLE;
    }

    @Override
    public Boolean add(SubjectInfoBo subjectInfoBo) {
        if (log.isDebugEnabled()) {
            log.debug("SubjectInfoDomainServiceImpl.add.bo:{}", JSON.toJSONString(subjectInfoBo));
        }
        //多选题目的插入
        List<SubjectMultiple> subjectMultipleList = new LinkedList<>();
        subjectInfoBo.getOptionList().forEach(option -> {
            SubjectMultiple subjectMultiple = MultipleConverter.INSTANCE.convertBoToEntity(option);
            subjectMultiple.setSubjectId(subjectInfoBo.getId());
            subjectMultiple.setIsDeleted(IsDeletedFlagEnum.UN_DELETED.getCode());
            subjectMultipleList.add(subjectMultiple);
        });
        return subjectMultipleService.insertBatch(subjectMultipleList);
    }

    @Override
    public SubjectOptionBo query(int subjectId) {
        SubjectMultiple subjectMultiple = new SubjectMultiple();
        subjectMultiple.setSubjectId(Long.valueOf(subjectId));
        List<SubjectMultiple> result = subjectMultipleService.queryByCondition(subjectMultiple);
        List<SubjectAnswerBo> SubjectAnswerBoList = MultipleConverter.INSTANCE.convertEntityToBoList(result);
        SubjectOptionBo subjectOptionBO = new SubjectOptionBo();
        subjectOptionBO.setOptionList(SubjectAnswerBoList);
        return subjectOptionBO;
    }
}
