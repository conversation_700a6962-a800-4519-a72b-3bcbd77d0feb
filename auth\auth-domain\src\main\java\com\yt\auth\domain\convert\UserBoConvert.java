package com.yt.auth.domain.convert;


import com.yt.auth.domain.entity.AuthUserBo;
import com.yt.auth.infra.basic.entity.AuthUser;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface UserBoConvert {
    UserBoConvert INSTANCE = Mappers.getMapper(UserBoConvert.class);
    AuthUser boToy(AuthUserBo authRolePermissionBo);
    AuthUserBo yToBo(AuthUser authUser);
}
