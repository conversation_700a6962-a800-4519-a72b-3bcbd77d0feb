spring:
  application:
    name: auth  # 应用名称，用于 Nacos 配置的 Data ID
  cloud:
    nacos:
      config:
        server-addr: 101.126.80.1:8848  # Nacos 配置中心的服务器地址
        prefix: ${spring.application.name}  # Nacos 配置的 Data ID 前缀，默认为应用名称
        group: DEFAULT_GROUP              # Nacos 配置的 Group，默认为 DEFAULT_GROUP
        namespace:                         # Nacos 命名空间 ID，如果使用命名空间，需要配置
        file-extension: yaml                # Nacos 配置的文件扩展名，默认为 yaml
      discovery:
        enabled: true                     # 启用 Nacos 服务发现
        server-addr: 101.126.80.1:8848  # Nacos 服务发现的服务器地址