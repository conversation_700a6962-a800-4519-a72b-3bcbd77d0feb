package com.yt.auth.infra.basic.service.impl;

import com.yt.auth.infra.basic.entity.AuthPermission;
import com.yt.auth.infra.basic.dao.AuthPermissionDao;
import com.yt.auth.infra.basic.service.AuthPermissionService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * (AuthPermission)表服务实现类
 */
@Service("authPermissionService")
public class AuthPermissionServiceImpl implements AuthPermissionService {
    @Resource
    private AuthPermissionDao authPermissionDao;

    /**
     * 通过ID查询单条数据
     */
    @Override
    public AuthPermission queryById(Long id) {
        return this.authPermissionDao.queryById(id);
    }


    /**
     * 新增数据
     */
    @Override
    public Boolean insert(AuthPermission authPermission) {
        return authPermissionDao.insert(authPermission)>0;
    }

    /**
     * 修改数据
     */
    @Override
    public Boolean update(AuthPermission authPermission) {
        return authPermissionDao.update(authPermission)>0;

    }

    /**
     * 通过主键删除数据
     */
    @Override
    public boolean deleteById(Long id) {
        return this.authPermissionDao.deleteById(id) > 0;
    }

    @Override
    public List<AuthPermission> queryByPermissionList(List<Long> permissionIds) {
        return authPermissionDao.queryAllByLimit(permissionIds);
    }
}
