<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yt.auth.infra.basic.dao.AuthRolePermissionDao">

    <resultMap type="com.yt.auth.infra.basic.entity.AuthRolePermission" id="AuthRolePermissionMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="roleId" column="role_id" jdbcType="INTEGER"/>
        <result property="permissionId" column="permission_id" jdbcType="INTEGER"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="AuthRolePermissionMap">
        select * from auth_role_permission
        <where>
        <if test="id != null">
            and id = #{id}
        </if>
        <if test="roleId != null">
            and role_id = #{roleId}
        </if>
        <if test="permissionId != null">
            and permission_id = #{permissionId}
        </if>
        <if test="createdBy != null and createdBy != ''">
            and created_by = #{createdBy}
        </if>
        <if test="createdTime != null">
            and created_time = #{createdTime}
        </if>
        <if test="updateBy != null and updateBy != ''">
            and update_by = #{updateBy}
        </if>
        <if test="updateTime != null">
            and update_time = #{updateTime}
        </if>
        <if test="isDeleted != null">
            and is_deleted = #{isDeleted}
        </if>
    </where>
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="AuthRolePermissionMap">
        select
        id, role_id, permission_id, created_by, created_time, update_by, update_time, is_deleted
        from auth_role_permission
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="roleId != null">
                and role_id = #{roleId}
            </if>
            <if test="permissionId != null">
                and permission_id = #{permissionId}
            </if>
            <if test="createdBy != null and createdBy != ''">
                and created_by = #{createdBy}
            </if>
            <if test="createdTime != null">
                and created_time = #{createdTime}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and update_by = #{updateBy}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="isDeleted != null">
                and is_deleted = #{isDeleted}
            </if>
        </where>
    </select>

    <select id="queryByRoleIds" resultMap="AuthRolePermissionMap">
        select
        id, role_id, permission_id, created_by, created_time, update_by, update_time, is_deleted
        from auth_role_permission
        where role_id in
        <foreach collection="list" item="roleId" open="(" separator="," close=")">
            #{roleId}
        </foreach>
        and is_deleted = 0
    </select>
    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from auth_role_permission
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="roleId != null">
                and role_id = #{roleId}
            </if>
            <if test="permissionId != null">
                and permission_id = #{permissionId}
            </if>
            <if test="createdBy != null and createdBy != ''">
                and created_by = #{createdBy}
            </if>
            <if test="createdTime != null">
                and created_time = #{createdTime}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and update_by = #{updateBy}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="isDeleted != null">
                and is_deleted = #{isDeleted}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into auth_role_permission(role_id, permission_id, created_by, created_time, update_by, update_time, is_deleted)
        values (#{roleId}, #{permissionId}, #{createdBy}, #{createdTime}, #{updateBy}, #{updateTime}, #{isDeleted})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into auth_role_permission(role_id, permission_id, created_by, created_time, update_by, update_time, is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.roleId}, #{entity.permissionId}, #{entity.createdBy}, #{entity.createdTime}, #{entity.updateBy}, #{entity.updateTime}, #{entity.isDeleted})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into auth_role_permission(role_id, permission_id, created_by, created_time, update_by, update_time, is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.roleId}, #{entity.permissionId}, #{entity.createdBy}, #{entity.createdTime}, #{entity.updateBy}, #{entity.updateTime}, #{entity.isDeleted})
        </foreach>
        on duplicate key update
role_id = values(role_id),
permission_id = values(permission_id),
created_by = values(created_by),
created_time = values(created_time),
update_by = values(update_by),
update_time = values(update_time),
is_deleted = values(is_deleted)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update auth_role_permission
        <set>
            <if test="roleId != null">
                role_id = #{roleId},
            </if>
            <if test="permissionId != null">
                permission_id = #{permissionId},
            </if>
            <if test="createdBy != null and createdBy != ''">
                created_by = #{createdBy},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from auth_role_permission where id = #{id}
    </delete>

</mapper>

