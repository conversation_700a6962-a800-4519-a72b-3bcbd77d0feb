package com.yt.subject.domain.service;

import com.yt.subject.domain.entity.SubjectLabelBo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SubjectLabelDomainService {

    boolean add(SubjectLabelBo subjectLabelBo);

    boolean update(SubjectLabelBo subjectLabelBo);

    boolean delete(SubjectLabelBo subjectLabelBo);

    List<SubjectLabelBo> queryLabelByCategoryId(SubjectLabelBo subjectLabelBo);
}
