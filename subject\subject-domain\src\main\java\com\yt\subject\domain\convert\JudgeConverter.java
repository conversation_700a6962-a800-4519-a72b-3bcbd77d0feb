package com.yt.subject.domain.convert;

import com.yt.subject.domain.entity.SubjectAnswerBo;
import com.yt.subject.infra.basic.entity.SubjectJudge;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface JudgeConverter {

    JudgeConverter INSTANCE = Mappers.getMapper(JudgeConverter.class);

    List<SubjectAnswerBo> convertEntityToBoList(List<SubjectJudge> subjectJudgeList);

}
