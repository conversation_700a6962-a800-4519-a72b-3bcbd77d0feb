package com.yt.auth.domain.service.impl;


import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.yt.auth.commom.enums.IsDeletedFlagEnum;
import com.yt.auth.commom.utils.RedisUtil;
import com.yt.auth.domain.convert.PermissionBoConvert;
import com.yt.auth.domain.entity.AuthPermissionBo;
import com.yt.auth.domain.service.AuthPermissionDomainService;
import com.yt.auth.infra.basic.entity.AuthPermission;
import com.yt.auth.infra.basic.service.AuthPermissionService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class AuthPermissionDomainServiceImpl implements AuthPermissionDomainService{

    @Resource
    private AuthPermissionService authPermissionService;

    private final String authPermissionPrefix= "auth.Permission";

    @Resource
    private RedisUtil redisUtil;

    @Override
    public Boolean add(AuthPermissionBo authPermissionBo) {
        AuthPermission authPermission = PermissionBoConvert.INSTANCE.boToy(authPermissionBo);
        return authPermissionService.insert(authPermission);
    }

    @Override
    public Boolean update(AuthPermissionBo authPermissionBo) {
        AuthPermission authPermission = PermissionBoConvert.INSTANCE.boToy(authPermissionBo);
        return authPermissionService.update(authPermission);
    }

    @Override
    public Boolean delete(AuthPermissionBo authPermissionBo) {
        AuthPermission authPermission = PermissionBoConvert.INSTANCE.boToy(authPermissionBo);
        authPermission.setIsDeleted(IsDeletedFlagEnum.DELETED.getCode());
        return authPermissionService.update(authPermission);
    }

    @Override
    public List<String> getPermission(String userName) {
        String permissionKey = redisUtil.buildKey(authPermissionPrefix, userName);
        String permissionValue = redisUtil.get(permissionKey);
        if (StringUtils.isBlank(permissionValue)) {
            return Collections.emptyList();
        }
        List<AuthPermission> roleList = new Gson().fromJson(permissionValue, new TypeToken<List<AuthPermission>>(){}.getType());
        return roleList.stream().map(AuthPermission::getPermissionKey).collect(Collectors.toList());
    }


}
