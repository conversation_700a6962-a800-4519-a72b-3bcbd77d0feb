package com.yt.oss.config;

import io.minio.MinioClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MinioConfig {
    /**
     * minio地址
     */
    @Value("${minio.url}")
    private String url;

    @Value("${minio.accessKey}")
    private String accessKey;

    @Value("${minio.secretKey}")
    private String secretKey;

    @Bean
    public MinioClient getMinioClient() {
        MinioClient minioClient = null;
        try {
            minioClient = MinioClient.builder().endpoint(url).credentials(accessKey, secretKey).build();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return minioClient;
    }
}