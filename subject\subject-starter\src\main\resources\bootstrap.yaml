spring:
  application:
    name: subject # 确保在此处也定义应用程序名称
  cloud:
    nacos:
      config:
        server-addr: 101.126.80.1:8848  # Nacos 配置中心的服务器地址
        prefix: ${spring.application.name} # Nacos 配置的 Data ID 前缀
        group: DEFAULT_GROUP              # Nacos 配置的 Group
        file-extension: yaml                # Nacos 配置的文件扩展名
      discovery:
        enabled: true                     # 启用 Nacos 服务发现
        server-addr: 101.126.80.1:8848  # Nacos 服务发现的服务器地址