package com.yt.subject.infra.basic.service;

import com.yt.subject.infra.basic.entity.SubjectCategory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.List;

/**
 * 题目分类(SubjectCategory)表服务接口
 * <AUTHOR>
 */
public interface SubjectCategoryService {

    /**
     * 通过ID查询单条数据
     */
    SubjectCategory queryById(Long id);

    /**
     * 分页查询
     */
    Page<SubjectCategory> queryByPage(SubjectCategory subjectCategory, PageRequest pageRequest);

    /**
     * 新增数据
     */
    SubjectCategory insert(SubjectCategory subjectCategory);

    /**
     * 修改数据
     */
    Boolean update(SubjectCategory subjectCategory);


    List<SubjectCategory> queryCategory(SubjectCategory subjectCategory);

    Integer queryCount(Long id);
}
