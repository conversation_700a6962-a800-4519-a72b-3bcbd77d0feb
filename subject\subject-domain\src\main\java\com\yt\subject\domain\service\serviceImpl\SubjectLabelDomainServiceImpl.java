package com.yt.subject.domain.service.serviceImpl;

import com.yt.subject.commom.enums.CategoryTypeEnum;
import com.yt.subject.commom.enums.IsDeletedFlagEnum;
import com.yt.subject.domain.convert.LabelBoConvert;
import com.yt.subject.domain.entity.SubjectLabelBo;
import com.yt.subject.domain.service.SubjectLabelDomainService;
import com.yt.subject.infra.basic.entity.SubjectCategory;
import com.yt.subject.infra.basic.entity.SubjectLabel;
import com.yt.subject.infra.basic.entity.SubjectMapping;
import com.yt.subject.infra.basic.service.SubjectCategoryService;
import com.yt.subject.infra.basic.service.SubjectLabelService;
import com.yt.subject.infra.basic.service.SubjectMappingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class SubjectLabelDomainServiceImpl implements SubjectLabelDomainService {

    @Resource
    private SubjectLabelService subjectLabelService;

    @Resource
    private SubjectCategoryService subjectCategoryService;

    @Resource
    private SubjectMappingService subjectMappingService;

    @Override
    public boolean add(SubjectLabelBo subjectLabelBo) {
        SubjectLabel subjectLabel = LabelBoConvert.INSTANCE.boToY(subjectLabelBo);
        subjectLabel.setIsDeleted(IsDeletedFlagEnum.UN_DELETED.getCode());
        return subjectLabelService.insert(subjectLabel);
    }

    @Override
    public boolean update(SubjectLabelBo subjectLabelBo) {
        SubjectLabel subjectLabel = LabelBoConvert.INSTANCE.boToY(subjectLabelBo);
        return subjectLabelService.update(subjectLabel);
    }

    @Override
    public boolean delete(SubjectLabelBo subjectLabelBo) {
        SubjectLabel subjectLabel = LabelBoConvert.INSTANCE.boToY(subjectLabelBo);
        subjectLabel.setIsDeleted(IsDeletedFlagEnum.UN_DELETED.getCode());
        return subjectLabelService.update(subjectLabel);
    }

    @Override
    public List<SubjectLabelBo> queryLabelByCategoryId(SubjectLabelBo subjectLabelBo) {
        //如果当前分类是1级分类，则查询所有标签
        SubjectCategory subjectCategory = subjectCategoryService.queryById(subjectLabelBo.getCategoryId());
        if(CategoryTypeEnum.PRIMARY.getCode() == subjectCategory.getCategoryType()){
            SubjectLabel subjectLabel = new SubjectLabel();
            subjectLabel.setCategoryId(subjectLabelBo.getCategoryId());
            List<SubjectLabel> labelList = subjectLabelService.queryByCondition(subjectLabel);
            return LabelBoConvert.INSTANCE.yToBo(labelList);
        }
        //如果当前分类是2级分类
        Long categoryId = subjectLabelBo.getCategoryId();
        SubjectMapping subjectMapping = new SubjectMapping();
        subjectMapping.setCategoryId(categoryId);
        subjectMapping.setIsDeleted(IsDeletedFlagEnum.UN_DELETED.getCode());
        List<SubjectMapping> mappingList = subjectMappingService.queryLabelId(subjectMapping);
        if (CollectionUtils.isEmpty(mappingList)) {
            return Collections.emptyList();
        }
        List<Long> labelIdList = mappingList.stream().map(SubjectMapping::getLabelId).collect(Collectors.toList());
        List<SubjectLabel> labelList = subjectLabelService.batchQueryById(labelIdList);
        List<SubjectLabelBo> boList = new LinkedList<>();
        labelList.forEach(label -> {
            SubjectLabelBo bo = new SubjectLabelBo();
            bo.setId(label.getId());
            bo.setLabelName(label.getLabelName());
            bo.setCategoryId(categoryId);
            bo.setSortNum(label.getSortNum());
            boList.add(bo);
        });
        return boList;
    }
}
