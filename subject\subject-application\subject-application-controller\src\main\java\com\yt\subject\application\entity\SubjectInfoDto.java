package com.yt.subject.application.entity;
import com.yt.subject.commom.entity.PageInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 题目信息
 *
 * <AUTHOR>
 * @since 2025-07-11 12:43:38
 */
@Data
public class SubjectInfoDto extends PageInfo implements Serializable   {
    private static final long serialVersionUID = -53895087177517799L;
    /**
     * 主键
     */
    private Long id;
    /**
     * 题目名称
     */
    private String subjectName;
    /**
     * 题目难度
     */
    private Integer subjectDifficult;
    /**
     * 出题人名
     */
    private String settleName;
    /**
     * 题目类型 1单选 2多选 3判断 4简答
     */
    private Integer subjectType;
    /**
     * 题目分数
     */
    private Integer subjectScore;
    /**
     * 题目解析
     */
    private String subjectParse;
    /**
     * 题目答案
      */
    private String subjectAnswer;
    /**
     * 分类id
     */
    private List<Integer> categoryIds;
    /**
     * 标签id
     */
    private List<Integer> labelIds;
    /**
     *题目答案列表
     */
    private List<SubjectAnswerDto> optionList;

    private Integer categoryId;

    private Integer labelId;
}

