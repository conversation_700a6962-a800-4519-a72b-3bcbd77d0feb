package com.yt.wx.utils;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.yt.wx.config.WxConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class WxApiUtil {

    @Resource
    private WxConfig wxConfig;
    @Resource
    private RedisUtil redisUtil;

    private static final String ACCESS_TOKEN_URL = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s";
    private static final String SEND_CUSTOM_MSG_URL = "https://api.weixin.qq.com/cgi-bin/message/custom/send?access_token=%s";
    private static final String ACCESS_TOKEN_KEY = "wx:accessToken";

    /**
     * 获取 access_token
     * 优先从Redis缓存获取，如果不存在或即将过期，则重新请求并缓存
     * @return a ccess_token
     */
    public String getAccessToken() {
        if (redisUtil.exist(ACCESS_TOKEN_KEY)) {
            return redisUtil.get(ACCESS_TOKEN_KEY);
        }

        String url = String.format(ACCESS_TOKEN_URL, wxConfig.getAppId(), wxConfig.getAppSecret());
        String result = HttpUtil.get(url);
        log.info("获取AccessToken结果: {}", result);

        JSONObject jsonObject = JSONUtil.parseObj(result);
        String accessToken = jsonObject.getStr("access_token");
        Integer expiresIn = jsonObject.getInt("expires_in");

        if (accessToken != null) {
            // 提前5分钟过期，以防止临界点问题
            redisUtil.setNx(ACCESS_TOKEN_KEY, accessToken, (long) (expiresIn - 300), TimeUnit.SECONDS);
            return accessToken;
        } else {
            log.error("获取 AccessToken 失败: {}", result);
            return null;
        }
    }

    /**
     * 发送客服文本消息
     * @param toUser 接收者OpenID
     * @param content 消息内容
     */
    public void sendCustomTextMessage(String toUser, String content) {
        String accessToken = getAccessToken();
        if (accessToken == null) {
            log.error("无法发送客服消息，因为 accessToken 为空");
            return;
        }

        String url = String.format(SEND_CUSTOM_MSG_URL, accessToken);

        Map<String, Object> textMap = new HashMap<>();
        textMap.put("content", content);

        Map<String, Object> bodyMap = new HashMap<>();
        bodyMap.put("touser", toUser);
        bodyMap.put("msgtype", "text");
        bodyMap.put("text", textMap);

        String postBody = JSONUtil.toJsonStr(bodyMap);
        log.info("发送客服消息请求体: {}", postBody);

        String result = HttpUtil.post(url, postBody);
        log.info("发送客服消息响应: {}", result);
    }
}
