package com.yt.subject.infra.basic.service;

import com.yt.subject.infra.basic.entity.SubjectLabel;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.List;

/**
 * 题目标签表(SubjectLabel)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-11 12:44:19
 */
public interface SubjectLabelService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    SubjectLabel queryById(Long id);

    /**
     * 分页查询
     *
     * @param subjectLabel 筛选条件
     * @param pageRequest      分页对象
     * @return 查询结果
     */
    Page<SubjectLabel> queryByPage(SubjectLabel subjectLabel, PageRequest pageRequest);

    /**
     * 新增数据
     *
     * @param subjectLabel 实例对象
     * @return 实例对象
     */
    Boolean insert(SubjectLabel subjectLabel);

    /**
     * 修改数据
     */
    Boolean update(SubjectLabel subjectLabel);


    List<SubjectLabel> batchQueryById(List<Long> labelIdList);

    List<SubjectLabel> queryByCondition(SubjectLabel subjectLabel);
}
