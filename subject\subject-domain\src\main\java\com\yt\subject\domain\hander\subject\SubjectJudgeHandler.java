package com.yt.subject.domain.hander.subject;

import com.alibaba.fastjson.JSON;
import com.yt.subject.commom.enums.IsDeletedFlagEnum;
import com.yt.subject.commom.enums.SubjectInfoEnums;
import com.yt.subject.domain.convert.JudgeConverter;
import com.yt.subject.domain.entity.SubjectAnswerBo;
import com.yt.subject.domain.entity.SubjectInfoBo;
import com.yt.subject.domain.entity.SubjectOptionBo;
import com.yt.subject.infra.basic.entity.SubjectJudge;
import com.yt.subject.infra.basic.service.SubjectJudgeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class SubjectJudgeHandler implements SubjectTypeHandler{

    @Resource
    private SubjectJudgeService subjectJudgeService;

    @Override
    public SubjectInfoEnums getSubjectType() {
        return SubjectInfoEnums.JUDGE;
    }

    @Override
    public Boolean add(SubjectInfoBo subjectInfoBo) {
        if (log.isDebugEnabled()) {
            log.debug("SubjectInfoDomainServiceImpl.add.bo:{}", JSON.toJSONString(subjectInfoBo));
        }
        SubjectJudge subjectJudge = new SubjectJudge();
        SubjectAnswerBo subjectAnswerBO = subjectInfoBo.getOptionList().get(0);
        subjectJudge.setSubjectId(subjectInfoBo.getId());
        subjectJudge.setIsCorrect(subjectAnswerBO.getIsCorrect());
        subjectJudge.setIsDeleted(IsDeletedFlagEnum.UN_DELETED.getCode());
        return subjectJudgeService.insert(subjectJudge);
    }

    @Override
    public SubjectOptionBo query(int subjectId) {
        SubjectJudge subjectJudge = new SubjectJudge();
        subjectJudge.setSubjectId(Long.valueOf(subjectId));
        List<SubjectJudge> result = subjectJudgeService.queryByCondition(subjectJudge);
        List<SubjectAnswerBo> subjectAnswerBOList = JudgeConverter.INSTANCE.convertEntityToBoList(result);
        SubjectOptionBo subjectOptionBO = new SubjectOptionBo();
        subjectOptionBO.setOptionList(subjectAnswerBOList);
        return subjectOptionBO;
    }
}
