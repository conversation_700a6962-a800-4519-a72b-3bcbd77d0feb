package com.yt.subject.domain.convert;

import com.yt.subject.domain.entity.SubjectInfoBo;
import com.yt.subject.domain.entity.SubjectOptionBo;
import com.yt.subject.infra.basic.entity.SubjectInfo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface SubjectBoConvert {
    SubjectBoConvert INSTANCE = Mappers.getMapper(SubjectBoConvert.class);
    SubjectInfo boToY(SubjectInfoBo subjectInfoBo);
    List<SubjectInfoBo> yToBo(List<SubjectInfo> subjectInfoList);

    SubjectInfoBo convertOptionAndInfoToBo(SubjectOptionBo optionBO, SubjectInfo subjectInfo);
}
