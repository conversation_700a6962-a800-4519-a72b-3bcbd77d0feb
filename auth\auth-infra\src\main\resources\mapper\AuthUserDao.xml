<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yt.auth.infra.basic.dao.AuthUserDao">

    <resultMap type="com.yt.auth.infra.basic.entity.AuthUser" id="AuthUserMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="userName" column="user_name" jdbcType="VARCHAR"/>
        <result property="nickName" column="nick_name" jdbcType="VARCHAR"/>
        <result property="email" column="email" jdbcType="VARCHAR"/>
        <result property="phone" column="phone" jdbcType="VARCHAR"/>
        <result property="password" column="password" jdbcType="VARCHAR"/>
        <result property="sex" column="sex" jdbcType="INTEGER"/>
        <result property="avatar" column="avatar" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="introduce" column="introduce" jdbcType="VARCHAR"/>
        <result property="extJson" column="ext_json" jdbcType="VARCHAR"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
    </resultMap>

    <select id="queryById" resultMap="AuthUserMap">
        select * from auth_user where id = #{id} and is_deleted = 0
    </select>

    <select id="queryByUserName" resultMap="AuthUserMap">
        select * from auth_user where user_name = #{userName} and is_deleted = 0
    </select>

    <select id="queryAllByLimit" resultMap="AuthUserMap">
        select * from auth_user
        <where>
            <if test="userName != null and userName != ''">
                and user_name = #{userName}
            </if>
            <if test="nickName != null and nickName != ''">
                and nick_name = #{nickName}
            </if>
            <if test="email != null and email != ''">
                and email = #{email}
            </if>
            <if test="phone != null and phone != ''">
                and phone = #{phone}
            </if>
            and is_deleted = 0
        </where>
    </select>

    <select id="count" resultType="java.lang.Long">
        select count(1) from auth_user
        <where>
            <if test="userName != null and userName != ''">
                and user_name = #{userName}
            </if>
            <if test="nickName != null and nickName != ''">
                and nick_name = #{nickName}
            </if>
            <if test="email != null and email != ''">
                and email = #{email}
            </if>
            <if test="phone != null and phone != ''">
                and phone = #{phone}
            </if>
            and is_deleted = 0
        </where>
    </select>
    <select id="queryByUsername" resultType="java.lang.Integer">
        select count(1) from auth_user
        <where>
            <if test="userName != null and userName != ''">
                and user_name = #{userName}
            </if>
            <if test="nickName != null and nickName != ''">
                and nick_name = #{nickName}
            </if>
            <if test="email != null and email != ''">
                and email = #{email}
            </if>
            <if test="phone != null and phone != ''">
                and phone = #{phone}
            </if>
            and is_deleted = 0
        </where>
    </select>


    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into auth_user(user_name, nick_name, email, phone, password, sex, avatar, status, introduce, ext_json, created_by, created_time, update_by, update_time, is_deleted)
        values (#{userName}, #{nickName}, #{email}, #{phone}, #{password}, #{sex}, #{avatar}, #{status}, #{introduce}, #{extJson}, #{createdBy}, #{createdTime}, #{updateBy}, #{updateTime}, #{isDeleted})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into auth_user(user_name, nick_name, email, phone, password, sex, avatar, status, introduce, ext_json, created_by, created_time, update_by, update_time, is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.userName}, #{entity.nickName}, #{entity.email}, #{entity.phone}, #{entity.password}, #{entity.sex}, #{entity.avatar}, #{entity.status}, #{entity.introduce}, #{entity.extJson}, #{entity.createdBy}, #{entity.createdTime}, #{entity.updateBy}, #{entity.updateTime}, #{entity.isDeleted})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into auth_user(user_name, nick_name, email, phone, password, sex, avatar, status, introduce, ext_json, created_by, created_time, update_by, update_time, is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.userName}, #{entity.nickName}, #{entity.email}, #{entity.phone}, #{entity.password}, #{entity.sex}, #{entity.avatar}, #{entity.status}, #{entity.introduce}, #{entity.extJson}, #{entity.createdBy}, #{entity.createdTime}, #{entity.updateBy}, #{entity.updateTime}, #{entity.isDeleted})
        </foreach>
        on duplicate key update
        user_name = values(user_name),
        nick_name = values(nick_name),
        email = values(email),
        phone = values(phone),
        password = values(password),
        sex = values(sex),
        avatar = values(avatar),
        status = values(status),
        introduce = values(introduce),
        ext_json = values(ext_json),
        created_by = values(created_by),
        created_time = values(created_time),
        update_by = values(update_by),
        update_time = values(update_time),
        is_deleted = values(is_deleted)
    </insert>

    <update id="update">
        update auth_user
        <set>
            <if test="nickName != null and nickName != ''">
                nick_name = #{nickName},
            </if>
            <if test="email != null and email != ''">
                email = #{email},
            </if>
            <if test="phone != null and phone != ''">
                phone = #{phone},
            </if>
            <if test="password != null and password != ''">
                password = #{password},
            </if>
            <if test="sex != null">
                sex = #{sex},
            </if>
            <if test="avatar != null and avatar != ''">
                avatar = #{avatar},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="introduce != null and introduce != ''">
                introduce = #{introduce},
            </if>
            <if test="extJson != null and extJson != ''">
                ext_json = #{extJson},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted},
            </if>
        </set>
        where user_name=#{userName}
    </update>

    <update id="deleteById">
        update auth_user set is_deleted = 1 where id = #{id}
    </update>
    <update id="updateId">
        update auth_user
        <set>
            <if test="userName != null and userName != ''">
                user_name = #{userName},
            </if>
            <if test="nickName != null and nickName != ''">
                nick_name = #{nickName},
            </if>
            <if test="email != null and email != ''">
                email = #{email},
            </if>
            <if test="phone != null and phone != ''">
                phone = #{phone},
            </if>
            <if test="password != null and password != ''">
                password = #{password},
            </if>
            <if test="sex != null">
                sex = #{sex},
            </if>
            <if test="avatar != null and avatar != ''">
                avatar = #{avatar},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="introduce != null and introduce != ''">
                introduce = #{introduce},
            </if>
            <if test="extJson != null and extJson != ''">
                ext_json = #{extJson},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted},
            </if>
        </set>
        where id=#{id}
    </update>

</mapper>