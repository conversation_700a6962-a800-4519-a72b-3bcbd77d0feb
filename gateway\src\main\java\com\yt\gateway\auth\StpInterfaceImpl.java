package com.yt.gateway.auth;

import cn.dev33.satoken.stp.StpInterface;
import com.alibaba.nacos.api.utils.StringUtils;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.yt.gateway.entity.AuthPermission;
import com.yt.gateway.entity.AuthRole;
import com.yt.gateway.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;
/*
  自定义权限验证接口扩展
  <AUTHOR>
 */

/**
 * 1.重写redis 需要传入RedisConnectionFactory参数
 * 2.注解Bean的原因  覆盖原有jdk提供的RedisTemplate
 * <AUTHOR>
 */
@Component
@Slf4j
public class StpInterfaceImpl implements StpInterface {
    @Resource
    private RedisUtil redisUtil;
    private final String authPermissionPrefix= "auth.Permission";
    private final String authRolePrefix= "auth.Role";

    @Override
    public List<String> getPermissionList(Object loginId, String loginType) {
        log.info("=== StpInterfaceImpl.getPermissionList 开始 ===");
        log.info("StpInterfaceImpl.getPermissionList.loginId: {}, loginType: {}", loginId, loginType);
        List<String> result = getAuth(loginId, authPermissionPrefix);
        log.info("StpInterfaceImpl.getPermissionList.result: {}", result);
        log.info("=== StpInterfaceImpl.getPermissionList 结束 ===");
        return result;
    }

    @Override
    public List<String> getRoleList(Object loginId, String loginType) {
        log.info("=== StpInterfaceImpl.getRoleList 开始 ===");
        log.info("StpInterfaceImpl.getRoleList.loginId: {}, loginType: {}", loginId, loginType);
        List<String> result = getAuth(loginId, authRolePrefix);
        log.info("StpInterfaceImpl.getRoleList.result: {}", result);
        log.info("=== StpInterfaceImpl.getRoleList 结束 ===");
        return result;
    }

    public List<String> getAuth(Object loginId, String prefix) {
        log.info("=== StpInterfaceImpl.getAuth 开始 ===");
        log.info("StpInterfaceImpl.getAuth.loginId: {}", loginId);
        log.info("StpInterfaceImpl.getAuth.prefix: {}", prefix);

        if (loginId == null) {
            log.error("StpInterfaceImpl.getAuth.loginId为null，无法构建Redis key");
            return Collections.emptyList();
        }

        String authKey = redisUtil.buildKey(prefix, loginId.toString());
        log.info("StpInterfaceImpl.getAuth.构建的Redis key: {}", authKey);

        String authValue = redisUtil.get(authKey);
        log.info("StpInterfaceImpl.getAuth.从Redis获取的原始数据: {}", authValue);

        if(StringUtils.isBlank(authValue)) {
            log.warn("StpInterfaceImpl.getAuth.Redis中没有找到数据，key: {}", authKey);
            return Collections.emptyList();
        }

        List<String> authList = new LinkedList<>();
        try {
            if(authRolePrefix.equals(prefix)){
                log.info("StpInterfaceImpl.getAuth.解析角色数据");
                List<AuthRole> roleList = new Gson().fromJson(authValue, new TypeToken<List<AuthRole>>(){}.getType());
                log.info("StpInterfaceImpl.getAuth.解析得到的角色对象列表: {}", roleList);
                authList = roleList.stream().map(AuthRole::getRoleKey).collect(Collectors.toList());
                log.info("StpInterfaceImpl.getAuth.提取的角色key列表: {}", authList);
            } else if (authPermissionPrefix.equals(prefix)) {
                log.info("StpInterfaceImpl.getAuth.解析权限数据");
                List<AuthPermission> permissionList = new Gson().fromJson(authValue, new TypeToken<List<AuthPermission>>(){}.getType());
                log.info("StpInterfaceImpl.getAuth.解析得到的权限对象列表: {}", permissionList);
                authList = permissionList.stream().map(AuthPermission::getPermissionKey).collect(Collectors.toList());
                log.info("StpInterfaceImpl.getAuth.提取的权限key列表: {}", authList);
            } else {
                log.warn("StpInterfaceImpl.getAuth.未知的prefix类型: {}", prefix);
            }
        } catch (Exception e) {
            log.error("StpInterfaceImpl.getAuth.解析JSON数据时发生异常: {}", e.getMessage(), e);
            log.error("StpInterfaceImpl.getAuth.原始JSON数据: {}", authValue);
            return Collections.emptyList();
        }

        log.info("StpInterfaceImpl.getAuth.最终返回结果: {}", authList);
        log.info("=== StpInterfaceImpl.getAuth 结束 ===");
        return authList;
    }
}