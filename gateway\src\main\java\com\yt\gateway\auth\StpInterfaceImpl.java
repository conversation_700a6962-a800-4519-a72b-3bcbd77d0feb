package com.yt.gateway.auth;

import cn.dev33.satoken.stp.StpInterface;
import com.alibaba.nacos.api.utils.StringUtils;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.yt.gateway.entity.AuthPermission;
import com.yt.gateway.entity.AuthRole;
import com.yt.gateway.utils.RedisUtil;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;
/*
  自定义权限验证接口扩展
  <AUTHOR>
 */

/**
 * 1.重写redis 需要传入RedisConnectionFactory参数
 * 2.注解Bean的原因  覆盖原有jdk提供的RedisTemplate
 * <AUTHOR>
 */
@Component
public class StpInterfaceImpl implements StpInterface {
    @Resource
    private RedisUtil redisUtil;
    private final String authPermissionPrefix= "auth.Permission";
    private final String authRolePrefix= "auth.Role";

    @Override
    public List<String> getPermissionList(Object loginId, String loginType) {
        return getAuth(loginId, authPermissionPrefix);
    }
    @Override
    public List<String> getRoleList(Object loginId, String loginType) {
        return getAuth(loginId, authRolePrefix);
    }

    public List<String> getAuth(Object loginId, String prefix) {
        String authKey = redisUtil.buildKey(prefix, loginId.toString());
        String authValue = redisUtil.get(authKey);
        if(StringUtils.isBlank(authValue)) {
            return Collections.emptyList();
        }
        List<String> authList=new LinkedList<>();
        if(authRolePrefix.equals(prefix)){
            List<AuthRole> roleList = new Gson().fromJson(authValue, new TypeToken<List<AuthRole>>(){}.getType());
            authList=roleList.stream().map(AuthRole::getRoleKey).collect(Collectors.toList());
        } else if (authPermissionPrefix.equals(prefix)) {
            List<AuthPermission> roleList = new Gson().fromJson(authValue, new TypeToken<List<AuthPermission>>(){}.getType());
            authList=roleList.stream().map(AuthPermission::getPermissionKey).collect(Collectors.toList());
        }
        return authList;
    }
}