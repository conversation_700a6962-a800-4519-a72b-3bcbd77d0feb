package com.yt.subject.application.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Preconditions;
import com.yt.subject.application.convect.LabelDtoConvert;
import com.yt.subject.application.entity.SubjectLabelDto;
import com.yt.subject.commom.entity.Result;
import com.yt.subject.domain.entity.SubjectLabelBo;
import com.yt.subject.domain.service.SubjectLabelDomainService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/subject/label")
@Slf4j
public class SubjectLabelController {

    @Resource
    private SubjectLabelDomainService subjectLabelDomainService;

    @RequestMapping("/add")
    public Result<Object> add(@RequestBody SubjectLabelDto subjectLabelDto) {
        try {
            if(log.isDebugEnabled()){
                log.info("SubjectLabelController.add.SubjectLabelDto:{}",JSON.toJSONString(subjectLabelDto));
            }
            Preconditions.checkArgument(!StringUtils.isBlank(subjectLabelDto.getLabelName()),"标签名称不能为空");
            SubjectLabelBo bo = LabelDtoConvert.INSTANCE.dtoToBo(subjectLabelDto);
            boolean rs=subjectLabelDomainService.add(bo);
            return Result.success(rs);
        } catch (Exception e) {
            log.error("SubjectLabelController.add.error:{}", e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @RequestMapping("/update")
    public Result<Object> update(@RequestBody SubjectLabelDto subjectLabelDto) {
        try {
            if(log.isDebugEnabled()){
                log.info("SubjectLabelController.update.SubjectLabelDto:{}",JSON.toJSONString(subjectLabelDto));
            }
            Preconditions.checkNotNull(subjectLabelDto.getId(),"标签id不能为空");
            SubjectLabelBo dto = LabelDtoConvert.INSTANCE.dtoToBo(subjectLabelDto);
            boolean rs=subjectLabelDomainService.update(dto);
            return Result.success(rs);
        } catch (Exception e) {
            log.error("SubjectLabelController.update.error:{}", e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @RequestMapping("/delete")
    public Result<Object> delete(@RequestBody SubjectLabelDto subjectLabelDto) {
        try {
            if(log.isDebugEnabled()){
                log.info("SubjectLabelController.delete.SubjectLabelDto:{}",JSON.toJSONString(subjectLabelDto));
            }
            Preconditions.checkNotNull(subjectLabelDto.getId(),"标签id不能为空");
            SubjectLabelBo dto = LabelDtoConvert.INSTANCE.dtoToBo(subjectLabelDto);
            boolean rs=subjectLabelDomainService.delete(dto);
            return Result.success(rs);
        } catch (Exception e) {
            log.error("SubjectLabelController.delete.error:{}", e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    @RequestMapping("/queryLabelByCategoryId")
    public Result<List<SubjectLabelDto>> queryLabelByCategoryId(@RequestBody SubjectLabelDto subjectLabelDto) {
        try {
            if(log.isDebugEnabled()){
                log.info("SubjectLabelController.queryLabelByCategoryId.SubjectLabelDto:{}",JSON.toJSONString(subjectLabelDto));
            }
            Preconditions.checkNotNull(subjectLabelDto.getCategoryId(),"分类id不能为空");
            SubjectLabelBo dto = LabelDtoConvert.INSTANCE.dtoToBo(subjectLabelDto);
            List<SubjectLabelBo> boList=subjectLabelDomainService.queryLabelByCategoryId(dto);
            List<SubjectLabelDto> dtoList =LabelDtoConvert.INSTANCE.boToDto(boList);
            return Result.success(dtoList);
        } catch (Exception e) {
            log.error("SubjectLabelController.queryLabelByCategoryId.error:{}", e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }
}
