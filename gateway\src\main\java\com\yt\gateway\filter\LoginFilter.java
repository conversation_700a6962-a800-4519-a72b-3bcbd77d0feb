package com.yt.gateway.filter;

import cn.dev33.satoken.stp.SaTokenInfo;
import cn.dev33.satoken.stp.StpUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

@Component
@Slf4j
public class LoginFilter implements GlobalFilter{
    @SneakyThrows
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        ServerHttpRequest.Builder mutate = request.mutate();
        String url = request.getURI().getPath();

        log.info("=== LoginFilter 开始处理请求 ===");
        log.info("LoginFilter.filter.url: {}", url);

        // 打印所有请求头，特别关注token相关的
        HttpHeaders headers = request.getHeaders();
        log.info("LoginFilter.filter.所有请求头: {}", headers);

        // 检查常见的token传递方式
        String authHeader = headers.getFirst("Authorization");
        String tokenHeader = headers.getFirst("token");
        String saTokenHeader = headers.getFirst("satoken");
        log.info("LoginFilter.filter.Authorization头: {}", authHeader);
        log.info("LoginFilter.filter.token头: {}", tokenHeader);
        log.info("LoginFilter.filter.satoken头: {}", saTokenHeader);

        // 检查Cookie中的token
        String cookieHeader = headers.getFirst("Cookie");
        log.info("LoginFilter.filter.Cookie头: {}", cookieHeader);

        if (url.equals("/user/doLogin")) {
            log.info("LoginFilter.filter.登录请求，直接放行");
            return chain.filter(exchange);
        }

        try {
            // 检查当前是否已登录
            boolean isLogin = StpUtil.isLogin();
            log.info("LoginFilter.filter.当前登录状态: {}", isLogin);

            // 获取token信息
            SaTokenInfo tokenInfo = StpUtil.getTokenInfo();
            log.info("LoginFilter.filter.tokenInfo: {}", tokenInfo);

            if (tokenInfo == null) {
                log.error("LoginFilter.filter.tokenInfo为null，可能前端未传递token或token格式错误");
                throw new Exception("未获取到token信息");
            }

            String loginId = (String) tokenInfo.getLoginId();
            log.info("LoginFilter.filter.loginId: {}", loginId);

            // 检查token是否有效
            String tokenValue = tokenInfo.getTokenValue();
            log.info("LoginFilter.filter.tokenValue: {}", tokenValue);

            if (StringUtils.isEmpty(loginId)) {
                log.error("LoginFilter.filter.loginId为空，token可能已过期或无效");
                throw new Exception("用户loginId不存在");
            }

            log.info("LoginFilter.filter.token验证成功，添加loginId到请求头: {}", loginId);
            mutate.header("loginId", loginId);

        } catch (Exception e) {
            log.error("LoginFilter.filter.处理token时发生异常: {}", e.getMessage(), e);
            throw e;
        }

        log.info("=== LoginFilter 处理完成 ===");
        return chain.filter(exchange.mutate().request(request.mutate().build()).build());
    }

}



