package com.yt.auth.infra.basic.service;

import com.yt.auth.infra.basic.entity.AuthUser;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

/**
 * 用户信息表(AuthUser)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-17 17:04:41
 */
public interface AuthUserService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    AuthUser queryById(Long id);

    /**
     * 分页查询
     *
     * @param authUser 筛选条件
     * @return 查询结果
     */
    AuthUser queryBy(AuthUser authUser);

    /**
     * 新增数据
     *
     * @param authUser 实例对象
     * @return 实例对象
     */
    Integer insert(AuthUser authUser);

    /**
     * 修改数据
     *
     * @param authUser 实例对象
     */
    Boolean update(AuthUser authUser);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(Long id);

    Boolean updateId(AuthUser authUser);

    Boolean queryByUsername(AuthUser authUser);
}
