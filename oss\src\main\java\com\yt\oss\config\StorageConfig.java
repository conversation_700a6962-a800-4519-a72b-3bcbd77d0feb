package com.yt.oss.config;

import com.yt.oss.utils.AliAdapter;
import com.yt.oss.utils.MinioAdapter;
import com.yt.oss.utils.StorageAdapter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@RefreshScope
public class StorageConfig {

    @Value("${Storage.type}")
    private  String storageType;


    @Bean
    @RefreshScope
    public StorageAdapter getStorageService(){
        System.out.println("OSS==>"+storageType);
        if("minio".equals(storageType)){
            return new MinioAdapter();
        }else if("aliyun".equals(storageType)) {
            return new AliAdapter();
        }else {
            throw new RuntimeException("暂时未找到对应的文件存储处理器");
        }
    }

}
