package com.yt.auth.infra.basic.service;

import com.yt.auth.infra.basic.entity.AuthUserRole;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.List;

/**
 * 用户角色表(AuthUserRole)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-17 17:04:40
 */
public interface AuthUserRoleService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    AuthUserRole queryById(Long id);

    /**
     * 分页查询
     *
     * @param authUserRole 筛选条件
     * @param pageRequest      分页对象
     * @return 查询结果
     */
    Page<AuthUserRole> queryByPage(AuthUserRole authUserRole, PageRequest pageRequest);

    /**
     * 新增数据
     *
     * @param authUserRole 实例对象
     */
    Boolean insert(AuthUserRole authUserRole);

    /**
     * 修改数据
     *
     * @param authUserRole 实例对象
     */
    Boolean update(AuthUserRole authUserRole);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(Long id);

    List<AuthUserRole> queryByCondition(AuthUserRole userRoleQuery);
}
