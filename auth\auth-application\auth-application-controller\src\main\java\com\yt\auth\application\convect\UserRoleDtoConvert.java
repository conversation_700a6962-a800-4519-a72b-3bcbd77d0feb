package com.yt.auth.application.convect;


import com.yt.auth.application.entity.AuthUserRoleDto;
import com.yt.auth.domain.entity.AuthUserRoleBo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface UserRoleDtoConvert {
    UserRoleDtoConvert INSTANCE = Mappers.getMapper(UserRoleDtoConvert.class);
    AuthUserRoleBo dtoToBo(AuthUserRoleDto authUserRoleDto);
}
