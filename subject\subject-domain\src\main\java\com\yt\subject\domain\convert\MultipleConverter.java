package com.yt.subject.domain.convert;


import com.yt.subject.domain.entity.SubjectAnswerBo;
import com.yt.subject.infra.basic.entity.SubjectMultiple;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MultipleConverter {

    MultipleConverter INSTANCE = Mappers.getMapper(MultipleConverter.class);

    SubjectMultiple convertBoToEntity(SubjectAnswerBo subjectAnswerBO);

    List<SubjectAnswerBo> convertEntityToBoList(List<SubjectMultiple> subjectMultipleList);


}
