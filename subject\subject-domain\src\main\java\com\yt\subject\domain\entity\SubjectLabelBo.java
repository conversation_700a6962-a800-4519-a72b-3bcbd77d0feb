package com.yt.subject.domain.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * 题目标签表(SubjectLabel)实体类
 *
 * <AUTHOR>
 * @since 2025-07-11 12:44:19
 */
@Data
public class SubjectLabelBo implements Serializable {
    private static final long serialVersionUID = 789202327843204254L;
/**
     * 主键
     */
    private Long id;
/**
     * 标签分类
     */
    private String labelName;
/**
     * 排序
     */
    private Integer sortNum;


    private Long categoryId;


    private Integer isDeleted;
}

