package com.yt.auth.domain.service.impl;

import com.yt.auth.commom.enums.IsDeletedFlagEnum;
import com.yt.auth.domain.entity.AuthRolePermissionBo;
import com.yt.auth.domain.service.AuthRolePermissionDomainService;
import com.yt.auth.infra.basic.entity.AuthRolePermission;
import com.yt.auth.infra.basic.service.AuthRolePermissionService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class AuthRolePermissionDomainServiceImpl implements AuthRolePermissionDomainService {

    @Resource
    private AuthRolePermissionService authRolePermissionService;

    @Override
    public Boolean add(AuthRolePermissionBo authedRolePermissionBo) {
        List<AuthRolePermission> authRolePermissions=new LinkedList<>();
        Long roleId=authedRolePermissionBo.getRoleId();
        authedRolePermissionBo.getPermissionIdList().forEach(permissionId ->{
            AuthRolePermission authRolePermission=new AuthRolePermission();
            authRolePermission.setRoleId(roleId);
            authRolePermission.setPermissionId(permissionId);
            authRolePermission.setIsDeleted(IsDeletedFlagEnum.UN_DELETED.getCode());
            authRolePermissions.add(authRolePermission);
        });
        return authRolePermissionService.insertBatch(authRolePermissions);
    }
}
