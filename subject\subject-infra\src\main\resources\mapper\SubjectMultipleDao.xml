<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yt.subject.infra.basic.dao.SubjectMultipleDao">

    <resultMap type="com.yt.subject.infra.basic.entity.SubjectMultiple" id="SubjectMultipleMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="subjectId" column="subject_id" jdbcType="INTEGER"/>
        <result property="optionType" column="option_type" jdbcType="INTEGER"/>
        <result property="optionContent" column="option_content" jdbcType="VARCHAR"/>
        <result property="isCorrect" column="is_correct" jdbcType="INTEGER"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="SubjectMultipleMap">
        select
id, subject_id, option_type, option_content, is_correct, created_by, created_time, update_by, update_time, is_deleted
        from subject_multiple
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="SubjectMultipleMap">
        select
id, subject_id, option_type, option_content, is_correct, created_by, created_time, update_by, update_time, is_deleted
        from subject_multiple
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="subjectId != null">
                and subject_id = #{subjectId}
            </if>
            <if test="optionType != null">
                and option_type = #{optionType}
            </if>
            <if test="optionContent != null and optionContent != ''">
                and option_content = #{optionContent}
            </if>
            <if test="isCorrect != null">
                and is_correct = #{isCorrect}
            </if>
            <if test="createdBy != null and createdBy != ''">
                and created_by = #{createdBy}
            </if>
            <if test="createdTime != null">
                and created_time = #{createdTime}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and update_by = #{updateBy}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="isDeleted != null">
                and is_deleted = #{isDeleted}
            </if>
        </where>
        limit #{pageable.offset}, #{pageable.pageSize}
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from subject_multiple
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="subjectId != null">
                and subject_id = #{subjectId}
            </if>
            <if test="optionType != null">
                and option_type = #{optionType}
            </if>
            <if test="optionContent != null and optionContent != ''">
                and option_content = #{optionContent}
            </if>
            <if test="isCorrect != null">
                and is_correct = #{isCorrect}
            </if>
            <if test="createdBy != null and createdBy != ''">
                and created_by = #{createdBy}
            </if>
            <if test="createdTime != null">
                and created_time = #{createdTime}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and update_by = #{updateBy}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="isDeleted != null">
                and is_deleted = #{isDeleted}
            </if>
        </where>
    </select>
    <select id="queryByCondition" resultMap="SubjectMultipleMap">
        select *
        from subject_multiple
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="subjectId != null">
                and subject_id = #{subjectId}
            </if>
            <if test="optionType != null">
                and option_type = #{optionType}
            </if>
            <if test="optionContent != null and optionContent != ''">
                and option_content = #{optionContent}
            </if>
            <if test="isCorrect != null">
                and is_correct = #{isCorrect}
            </if>
            <if test="createdBy != null and createdBy != ''">
                and created_by = #{createdBy}
            </if>
            <if test="createdTime != null">
                and created_time = #{createdTime}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and update_by = #{updateBy}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="isDeleted != null">
                and is_deleted = #{isDeleted}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into subject_multiple(subject_id, option_type, option_content, is_correct, created_by, created_time, update_by, update_time, is_deleted)
        values (#{subjectId}, #{optionType}, #{optionContent}, #{isCorrect}, #{createdBy}, #{createdTime}, #{updateBy}, #{updateTime}, #{isDeleted})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into subject_multiple(subject_id, option_type, option_content, is_correct, created_by, created_time, update_by, update_time, is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.subjectId}, #{entity.optionType}, #{entity.optionContent}, #{entity.isCorrect}, #{entity.createdBy}, #{entity.createdTime}, #{entity.updateBy}, #{entity.updateTime}, #{entity.isDeleted})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into subject_multiple(subject_id, option_type, option_content, is_correct, created_by, created_time, update_by, update_time, is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.subjectId}, #{entity.optionType}, #{entity.optionContent}, #{entity.isCorrect}, #{entity.createdBy}, #{entity.createdTime}, #{entity.updateBy}, #{entity.updateTime}, #{entity.isDeleted})
        </foreach>
        on duplicate key update
subject_id = values(subject_id),
option_type = values(option_type),
option_content = values(option_content),
is_correct = values(is_correct),
created_by = values(created_by),
created_time = values(created_time),
update_by = values(update_by),
update_time = values(update_time),
is_deleted = values(is_deleted)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update subject_multiple
        <set>
            <if test="subjectId != null">
                subject_id = #{subjectId},
            </if>
            <if test="optionType != null">
                option_type = #{optionType},
            </if>
            <if test="optionContent != null and optionContent != ''">
                option_content = #{optionContent},
            </if>
            <if test="isCorrect != null">
                is_correct = #{isCorrect},
            </if>
            <if test="createdBy != null and createdBy != ''">
                created_by = #{createdBy},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from subject_multiple where id = #{id}
    </delete>

</mapper>

