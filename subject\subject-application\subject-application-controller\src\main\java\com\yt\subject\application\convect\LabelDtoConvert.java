package com.yt.subject.application.convect;

import com.yt.subject.application.entity.SubjectLabelDto;
import com.yt.subject.domain.entity.SubjectLabelBo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface LabelDtoConvert {
    LabelDtoConvert INSTANCE = Mappers.getMapper(LabelDtoConvert.class);
    SubjectLabelBo dtoToBo(SubjectLabelDto subjectLabelDto);
    List<SubjectLabelDto> boToDto(List<SubjectLabelBo> subjectLabelBoList);

}
