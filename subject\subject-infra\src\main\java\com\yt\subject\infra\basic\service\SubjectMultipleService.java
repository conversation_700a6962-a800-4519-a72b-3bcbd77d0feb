package com.yt.subject.infra.basic.service;

import com.yt.subject.infra.basic.entity.SubjectMultiple;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.List;

/**
 * 多选题信息表(SubjectMultiple)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-11 12:45:15
 */
public interface SubjectMultipleService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    SubjectMultiple queryById(Long id);

    /**
     * 分页查询
     *
     * @param subjectMultiple 筛选条件
     * @param pageRequest      分页对象
     * @return 查询结果
     */
    Page<SubjectMultiple> queryByPage(SubjectMultiple subjectMultiple, PageRequest pageRequest);

    /**
     * 新增数据
     *
     * @param subjectMultiple 实例对象
     * @return 实例对象
     */
    SubjectMultiple insert(SubjectMultiple subjectMultiple);

    /**
     * 修改数据
     *
     * @param subjectMultiple 实例对象
     * @return 实例对象
     */
    SubjectMultiple update(SubjectMultiple subjectMultiple);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(Long id);

    Boolean insertBatch(List<SubjectMultiple> multipleList);

    List<SubjectMultiple> queryByCondition(SubjectMultiple subjectMultiple);
}
