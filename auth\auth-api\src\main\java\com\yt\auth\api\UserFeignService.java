package com.yt.auth.api;

import com.yt.auth.entity.AuthUserDto;

import com.yt.auth.entity.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
@FeignClient("auth")
public interface UserFeignService {
    @RequestMapping("/getUserInfo")
    public Result<AuthUserDto> getUserInfo(@RequestBody AuthUserDto authUserDto);
}
