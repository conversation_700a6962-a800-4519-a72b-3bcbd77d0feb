package com.yt.auth.application.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * (AuthRole)实体类
 *
 * <AUTHOR>
 * @since 2025-07-17 17:04:41
 */
@Data
public class AuthRoleDto implements Serializable {
    private static final long serialVersionUID = -44091039749436790L;

    private Long id;
/**
     * 角色名称
     */
    private String roleName;
/**
     * 角色唯一标识
     */
    private String roleKey;
/**
     * 是否被删除 0未删除 1已删除
     */
    private Integer isDeleted;


}

