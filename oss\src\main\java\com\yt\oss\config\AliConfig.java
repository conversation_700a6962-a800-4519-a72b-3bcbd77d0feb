package com.yt.oss.config;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class AliConfig {
    /**
     * minio地址
     */
    @Value("${AliOSS.endpoint}")
    private String endpoint;

    @Value("${AliOSS.accessKeyId}")
    private String accessKey;

    @Value("${AliOSS.accessKeySecret}")
    private String secretKey;


    @Bean
    public OSS ossClient() {
        return new OSSClientBuilder().build(endpoint,accessKey,secretKey);
    }

}