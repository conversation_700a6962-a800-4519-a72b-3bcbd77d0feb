package com.yt.subject.infra.basic.service.impl;

import com.yt.subject.infra.basic.entity.SubjectJudge;
import com.yt.subject.infra.basic.dao.SubjectJudgeDao;
import com.yt.subject.infra.basic.service.SubjectJudgeService;
import org.springframework.stereotype.Service;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import javax.annotation.Resource;
import java.util.List;

/**
 * 判断题(SubjectJudge)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-11 12:44:03
 */
@Service("subjectJudgeService")
public class SubjectJudgeServiceImpl implements SubjectJudgeService {
    @Resource
    private SubjectJudgeDao subjectJudgeDao;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public SubjectJudge queryById(Long id) {
        return this.subjectJudgeDao.queryById(id);
    }

    /**
     * 分页查询
     *
     * @param subjectJudge 筛选条件
     * @param pageRequest      分页对象
     * @return 查询结果
     */
    @Override
    public Page<SubjectJudge> queryByPage(SubjectJudge subjectJudge, PageRequest pageRequest) {
        long total = this.subjectJudgeDao.count(subjectJudge);
        return new PageImpl<>(this.subjectJudgeDao.queryAllByLimit(subjectJudge, pageRequest), pageRequest, total);
    }

    /**
     * 新增数据
     *
     * @param subjectJudge 实例对象
     * @return 实例对象
     */
    @Override
    public Boolean insert(SubjectJudge subjectJudge) {
        return subjectJudgeDao.insert(subjectJudge)>0;
    }

    /**
     * 修改数据
     *
     * @param subjectJudge 实例对象
     * @return 实例对象
     */
    @Override
    public SubjectJudge update(SubjectJudge subjectJudge) {
        this.subjectJudgeDao.update(subjectJudge);
        return this.queryById(subjectJudge.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(Long id) {
        return this.subjectJudgeDao.deleteById(id) > 0;
    }

    @Override
    public List<SubjectJudge> queryByCondition(SubjectJudge subjectJudge) {
        return this.subjectJudgeDao.queryByCondition(subjectJudge);
    }
}
