spring:
  cloud:
    gateway:
      routes:
        # OSS (对象存储) 服务的路由配置
        - id: oss
          uri: lb://oss
          predicates:
            - Path=/oss/**
          filters:
            - StripPrefix=1
        # Auth (认证) 服务的路由配置
        - id: auth
          uri: lb://auth
          predicates:
            - Path=/auth/**
          filters:
            - StripPrefix=1
        # Subject (题目) 服务的路由配置
        - id: subject
          uri: lb://subject
          predicates:
            - Path=/subject/**
          filters:
            - StripPrefix=1
  redis:
    database: 1
    host: **************
    port: 6379
    password: 123456
    timeout: 10s
    lettuce:
      pool:
        max-active: 200
        max-wait: -1ms
        max-idle: 10
        min-idle: 0
sa-token:
  # token 名称（同时也是 cookie 名称）
  token-name: token
  # token 有效期（单位：秒） 默认30天，-1 代表永久有效
  timeout: 2592000
  # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
  active-timeout: -1
  # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
  is-share: false
  # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
  token-style: uuid
  # 是否输出操作日志
  is-log: true
  # 指定 token 提交时的前缀
  token-prefix: yt