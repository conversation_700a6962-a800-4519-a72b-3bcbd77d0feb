package com.yt.auth.domain.convert;


import com.yt.auth.domain.entity.AuthPermissionBo;
import com.yt.auth.infra.basic.entity.AuthPermission;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface PermissionBoConvert {
    PermissionBoConvert INSTANCE = Mappers.getMapper(PermissionBoConvert.class);
    AuthPermission boToy(AuthPermissionBo permissionBo);
}
