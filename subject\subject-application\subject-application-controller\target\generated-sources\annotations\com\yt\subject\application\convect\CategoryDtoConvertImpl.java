package com.yt.subject.application.convect;

import com.yt.subject.application.entity.SubjectCategoryDto;
import com.yt.subject.domain.entity.SubjectCategoryBo;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-29T01:01:30+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
public class CategoryDtoConvertImpl implements CategoryDtoConvert {

    @Override
    public SubjectCategoryBo dtoToBo(SubjectCategoryDto subjectCategoryDto) {
        if ( subjectCategoryDto == null ) {
            return null;
        }

        SubjectCategoryBo subjectCategoryBo = new SubjectCategoryBo();

        subjectCategoryBo.setId( subjectCategoryDto.getId() );
        subjectCategoryBo.setCategoryName( subjectCategoryDto.getCategoryName() );
        subjectCategoryBo.setCategoryType( subjectCategoryDto.getCategoryType() );
        subjectCategoryBo.setImageUrl( subjectCategoryDto.getImageUrl() );
        subjectCategoryBo.setParentId( subjectCategoryDto.getParentId() );
        subjectCategoryBo.setCount( subjectCategoryDto.getCount() );

        return subjectCategoryBo;
    }

    @Override
    public List<SubjectCategoryDto> boToDtoList(List<SubjectCategoryBo> subjectCategoryBoList) {
        if ( subjectCategoryBoList == null ) {
            return null;
        }

        List<SubjectCategoryDto> list = new ArrayList<SubjectCategoryDto>( subjectCategoryBoList.size() );
        for ( SubjectCategoryBo subjectCategoryBo : subjectCategoryBoList ) {
            list.add( boToDto( subjectCategoryBo ) );
        }

        return list;
    }

    @Override
    public SubjectCategoryDto boToDto(SubjectCategoryBo subjectCategoryBoList) {
        if ( subjectCategoryBoList == null ) {
            return null;
        }

        SubjectCategoryDto subjectCategoryDto = new SubjectCategoryDto();

        subjectCategoryDto.setId( subjectCategoryBoList.getId() );
        subjectCategoryDto.setCategoryName( subjectCategoryBoList.getCategoryName() );
        subjectCategoryDto.setCategoryType( subjectCategoryBoList.getCategoryType() );
        subjectCategoryDto.setImageUrl( subjectCategoryBoList.getImageUrl() );
        subjectCategoryDto.setParentId( subjectCategoryBoList.getParentId() );
        subjectCategoryDto.setCount( subjectCategoryBoList.getCount() );

        return subjectCategoryDto;
    }
}
