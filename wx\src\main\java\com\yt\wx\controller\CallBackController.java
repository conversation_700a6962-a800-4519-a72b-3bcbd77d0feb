package com.yt.wx.controller;

import com.yt.wx.handler.WxChatMsgFactory;
import com.yt.wx.handler.WxChatMsgHandler;
import com.yt.wx.utils.MessageUtil;
import com.yt.wx.utils.Sha1;
import com.yt.wx.utils.WxApiUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@Slf4j
public class CallBackController {

    private static final String TOKEN = "qtz学不会";

    @Resource
    private WxChatMsgFactory wxChatMsgFactory;

    @Resource
    private WxApiUtil wxApiUtil;

    /**
     * 回调消息校验
     */
    @GetMapping("/callback")
    public String callback(@RequestParam("signature") String signature,
                           @RequestParam("timestamp") String timestamp,
                           @RequestParam("nonce") String nonce,
                           @RequestParam("echostr") String echostr) {
        log.info("微信签名校验: signature={}, timestamp={}, nonce={}, echostr={}", signature, timestamp, nonce, echostr);
         if (Sha1.getSa1(TOKEN, timestamp, nonce, "").equals(signature)) {
             return echostr;
         }
        return echostr;
    }

    /**
     * 接收微信消息的回调接口
     */
    @PostMapping(value = "/callback", produces = "application/xml;charset=UTF-8")
    public String callback(@RequestBody String requestBody) {
        log.info("接收到微信消息：requestBody：{}", requestBody);
        try {
            Map<String, String> messageMap = MessageUtil.parseXml(requestBody);
            // 异步处理消息
            handleMessageAsync(messageMap);
        } catch (Exception e) {
            log.error("解析微信消息失败: ", e);
        }
        // 立即返回空字符串，告知微信服务器已成功接收，无需重试
        return "";
    }

    /**
     * 使用 @Async 注解进行异步处理
     * @param messageMap 解析后的消息内容
     */
    @Async("taskExecutor") // 确保您已配置了名为 taskExecutor 的线程池，或使用默认线程池
    public void handleMessageAsync(Map<String, String> messageMap) {
        String msgType = messageMap.get("MsgType");
        String event = messageMap.get("Event");
        log.info("开始异步处理消息 -> msgType:{}, event:{}", msgType, event);

        String msgTypeKey = msgType;
        if (event != null && !event.isEmpty()) {
            msgTypeKey = msgType + "." + event;
        }

        WxChatMsgHandler wxChatMsgHandler = wxChatMsgFactory.getHandlerByMsgType(msgTypeKey);
        if (wxChatMsgHandler != null) {
            // dealMsg 直接返回要回复的文本内容
            String contentToReply = wxChatMsgHandler.dealMsg(messageMap);

            if (contentToReply != null && !contentToReply.isEmpty()) {
                log.info("异步处理完成，准备通过客服消息接口发送: {}", contentToReply);
                try {
                    // 获取接收者 OpenID (即消息的发送者)
                    String toUser = messageMap.get("FromUserName");

                    // 调用客服消息接口将内容发送给用户
                    wxApiUtil.sendCustomTextMessage(toUser, contentToReply);

                } catch (Exception e) {
                    log.error("发送客服消息时发生异常: ", e);
                }
            } else {
                log.info("异步处理完成，无内容需要回复。");
            }
        } else {
            log.warn("未找到对应的消息处理器，msgTypeKey: {}", msgTypeKey);
        }
    }
}