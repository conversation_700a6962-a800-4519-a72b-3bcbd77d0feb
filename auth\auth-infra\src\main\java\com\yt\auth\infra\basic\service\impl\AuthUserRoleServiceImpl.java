package com.yt.auth.infra.basic.service.impl;

import com.yt.auth.infra.basic.entity.AuthUserRole;
import com.yt.auth.infra.basic.dao.AuthUserRoleDao;
import com.yt.auth.infra.basic.service.AuthUserRoleService;
import org.springframework.stereotype.Service;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import javax.annotation.Resource;
import java.util.List;

/**
 * 用户角色表(AuthUserRole)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-17 17:04:40
 */
@Service("authUserRoleService")
public class AuthUserRoleServiceImpl implements AuthUserRoleService {
    @Resource
    private AuthUserRoleDao authUserRoleDao;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public AuthUserRole queryById(Long id) {
        return this.authUserRoleDao.queryById(id);
    }

    /**
     * 分页查询
     *
     * @param authUserRole 筛选条件
     * @param pageRequest      分页对象
     * @return 查询结果
     */
    @Override
    public Page<AuthUserRole> queryByPage(AuthUserRole authUserRole, PageRequest pageRequest) {
        long total = this.authUserRoleDao.count(authUserRole);
        return new PageImpl<>(this.authUserRoleDao.queryAllByLimit(authUserRole, pageRequest), pageRequest, total);
    }

    /**
     * 新增数据
     *
     * @param authUserRole 实例对象
     * @return 实例对象
     */
    @Override
    public Boolean insert(AuthUserRole authUserRole) {
        return authUserRoleDao.insert(authUserRole)>0;
    }

    /**
     * 修改数据
     *
     * @param authUserRole 实例对象
     * @return 实例对象
     */
    @Override
    public Boolean update(AuthUserRole authUserRole) {
        return authUserRoleDao.update(authUserRole)>0;
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(Long id) {
        return this.authUserRoleDao.deleteById(id) > 0;
    }

    @Override
    public List<AuthUserRole> queryByCondition(AuthUserRole userRoleQuery) {
        return this.authUserRoleDao.queryByCondition(userRoleQuery);
    }
}
