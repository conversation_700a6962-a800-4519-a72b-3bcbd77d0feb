package com.yt.subject.infra.basic.service.impl;

import com.yt.subject.infra.basic.entity.SubjectMapping;
import com.yt.subject.infra.basic.dao.SubjectMappingDao;
import com.yt.subject.infra.basic.service.SubjectMappingService;
import org.springframework.stereotype.Service;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import javax.annotation.Resource;
import java.util.List;

/**
 * 题目分类关系表(SubjectMapping)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-11 12:45:01
 */
@Service("subjectMappingService")
public class SubjectMappingServiceImpl implements SubjectMappingService {
    @Resource
    private SubjectMappingDao subjectMappingDao;

    /**
     * 通过ID查询单条数据
     *
     * @param subjectMapping 主键
     * @return 实例对象
     */
    @Override
    public List<SubjectMapping> queryLabelId(SubjectMapping subjectMapping) {
        return this.subjectMappingDao.queryById(subjectMapping);
    }

    /**
     * 分页查询
     *
     * @param subjectMapping 筛选条件
     * @param pageRequest      分页对象
     * @return 查询结果
     */
    @Override
    public Page<SubjectMapping> queryByPage(SubjectMapping subjectMapping, PageRequest pageRequest) {
        long total = this.subjectMappingDao.count(subjectMapping);
        return new PageImpl<>(this.subjectMappingDao.queryAllByLimit(subjectMapping, pageRequest), pageRequest, total);
    }

    /**
     * 新增数据
     *
     * @param subjectMapping 实例对象
     * @return 实例对象
     */
    @Override
    public SubjectMapping insert(SubjectMapping subjectMapping) {
        this.subjectMappingDao.insert(subjectMapping);
        return subjectMapping;
    }

    /**
     * 修改数据
     *
     * @param subjectMapping 实例对象
     * @return 实例对象
     */
    @Override
    public Boolean update(SubjectMapping subjectMapping) {
       return  subjectMappingDao.update(subjectMapping)>0;
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(Long id) {
        return this.subjectMappingDao.deleteById(id) > 0;
    }

    @Override
    public Boolean batchAdd(List<SubjectMapping> mappingList) {
        return subjectMappingDao.insertBatch(mappingList) > 0;
    }
}
