package com.yt.oss.utils;

import cn.hutool.core.io.IoUtil;
import io.minio.*;
import io.minio.http.Method;
import io.minio.messages.Bucket;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * minio操作工具类
 * <AUTHOR>
 */

@Slf4j
public class MinioAdapter implements StorageAdapter {

    @Resource
    private MinioClient minioClient;

    @Value("${minio.url}")
    private String url;

    /**
     * (新增) 为Minio实现获取预签名URL的方法
     */
    @Override
    public String getPresignedObjectUrl(String bucketName, String objectName) {
        try {
            return minioClient.getPresignedObjectUrl(
                    GetPresignedObjectUrlArgs.builder()
                            .method(Method.GET)
                            .bucket(bucketName)
                            .object(objectName)
                            .expiry(1, TimeUnit.HOURS)
                            .build());
        } catch (Exception e) {
            log.error("Minio 预览图片错误: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * (新增) 实现以字节数组下载文件
     */
    @Override
    public byte[] downloadFileAsBytes(String fileName, String bucketName) {
        GetObjectArgs args = GetObjectArgs.builder()
                .bucket(bucketName)
                .object(fileName)
                .build();
        try (GetObjectResponse response = minioClient.getObject(args)) {
            return IoUtil.readBytes(response);
        } catch (Exception e) {
            log.error("Minio文件下载错误: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 列出所有存储桶的存储桶信息
     */
    @Override
    public List<String> listBuckets() {
        List<String> result = new ArrayList<>();
        try {
            List<Bucket> buckets = minioClient.listBuckets();
            result = buckets.stream().map(Bucket::name).collect(Collectors.toList());
        } catch (Exception e) {
            log.info("listBuckets exception......",e);
        }
        return result;

    }

    /**
     * 文件上传
     */
    @Override
    public String uploadFile(MultipartFile uploadFile, String bucket, String objectName) {
        try {
            // 检查 bucket 是否存在，如果不存在则创建
            boolean found = minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucket).build());
            if (!found) {
                minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucket).build());
                log.info("Bucket '{}' created successfully.", bucket);
            }

            InputStream is = uploadFile.getInputStream();
            minioClient.putObject(
                    PutObjectArgs.builder()
                            .bucket(bucket)
                            .object(objectName)
                            .stream(is, uploadFile.getSize(), -1)
                            .contentType(uploadFile.getContentType())
                            .build());
            is.close();

            // 拼接并返回URL
            String url = this.url + "/" + bucket + "/" + objectName;
            log.info("File upload success, URL: {}", url);
            return url;
        } catch (Exception e) {
            log.error("File upload exception", e);
            return null;
        }
    }
}