<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yt.subject.infra.basic.dao.SubjectCategoryDao">

    <resultMap type="com.yt.subject.infra.basic.entity.SubjectCategory" id="SubjectCategoryMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="categoryName" column="category_name" jdbcType="VARCHAR"/>
        <result property="categoryType" column="category_type" jdbcType="INTEGER"/>
        <result property="imageUrl" column="image_url" jdbcType="VARCHAR"/>
        <result property="parentId" column="parent_id" jdbcType="INTEGER"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="SubjectCategoryMap">
        select
id, category_name, category_type, image_url, parent_id, created_by, created_time, update_by, update_time, is_deleted
        from subject_category
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="SubjectCategoryMap">
        select
id, category_name, category_type, image_url, parent_id, created_by, created_time, update_by, update_time, is_deleted
        from subject_category
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="categoryName != null and categoryName != ''">
                and category_name = #{categoryName}
            </if>
            <if test="categoryType != null">
                and category_type = #{categoryType}
            </if>
            <if test="imageUrl != null and imageUrl != ''">
                and image_url = #{imageUrl}
            </if>
            <if test="parentId != null">
                and parent_id = #{parentId}
            </if>
            <if test="createdBy != null and createdBy != ''">
                and created_by = #{createdBy}
            </if>
            <if test="createdTime != null">
                and created_time = #{createdTime}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and update_by = #{updateBy}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="isDeleted != null">
                and is_deleted = #{isDeleted}
            </if>
        </where>
        limit #{pageable.offset}, #{pageable.pageSize}
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Integer">
        select count(distinct subject_id)
        from subject_mapping a,subject_label b
       where a.label_id=b.id and b.category_id=#{id}
    </select>
    <select id="queryCategory" resultMap="SubjectCategoryMap">
        select * from subject_category
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="categoryName != null and categoryName != ''">
                and category_name = #{categoryName}
            </if>
            <if test="categoryType != null">
                and category_type = #{categoryType}
            </if>
            <if test="imageUrl != null and imageUrl != ''">
                and image_url = #{imageUrl}
            </if>
            <if test="parentId != null">
                and parent_id = #{parentId}
            </if>
            <if test="createdBy != null and createdBy != ''">
                and created_by = #{createdBy}
            </if>
            <if test="createdTime != null">
                and created_time = #{createdTime}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and update_by = #{updateBy}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="isDeleted != null">
                and is_deleted = #{isDeleted}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into subject_category(category_name, category_type, image_url, parent_id, created_by, created_time, update_by, update_time, is_deleted)
        values (#{categoryName}, #{categoryType}, #{imageUrl}, #{parentId}, #{createdBy}, #{createdTime}, #{updateBy}, #{updateTime}, #{isDeleted})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into subject_category(category_name, category_type, image_url, parent_id, created_by, created_time, update_by, update_time, is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.categoryName}, #{entity.categoryType}, #{entity.imageUrl}, #{entity.parentId}, #{entity.createdBy}, #{entity.createdTime}, #{entity.updateBy}, #{entity.updateTime}, #{entity.isDeleted})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into subject_category(category_name, category_type, image_url, parent_id, created_by, created_time, update_by, update_time, is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.categoryName}, #{entity.categoryType}, #{entity.imageUrl}, #{entity.parentId}, #{entity.createdBy}, #{entity.createdTime}, #{entity.updateBy}, #{entity.updateTime}, #{entity.isDeleted})
        </foreach>
        on duplicate key update
category_name = values(category_name),
category_type = values(category_type),
image_url = values(image_url),
parent_id = values(parent_id),
created_by = values(created_by),
created_time = values(created_time),
update_by = values(update_by),
update_time = values(update_time),
is_deleted = values(is_deleted)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update subject_category
        <set>
            <if test="categoryName != null and categoryName != ''">
                category_name = #{categoryName},
            </if>
            <if test="categoryType != null">
                category_type = #{categoryType},
            </if>
            <if test="imageUrl != null and imageUrl != ''">
                image_url = #{imageUrl},
            </if>
            <if test="parentId != null">
                parent_id = #{parentId},
            </if>
            <if test="createdBy != null and createdBy != ''">
                created_by = #{createdBy},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from subject_category where id = #{id}
    </delete>

</mapper>

