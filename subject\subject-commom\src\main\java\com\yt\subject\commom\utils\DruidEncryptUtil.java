package com.yt.subject.commom.utils;

import com.alibaba.druid.filter.config.ConfigTools;

import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;

/**
 * 数据库加密工具类
 * 用于加密数据库连接信息，提高安全性
 * 
 * <AUTHOR>
 */
public class DruidEncryptUtil {

    private static String publicKey;
    private static String privateKey;

    static {
        try {
            String[] keyPair = ConfigTools.genKeyPair(512);
            privateKey = keyPair[0];
            System.out.println("privateKey: " + privateKey);
            publicKey = keyPair[1];
            System.out.println("publicKey: " + publicKey);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("生成密钥对失败: NoSuchAlgorithmException", e);
        } catch (NoSuchProviderException e) {
            throw new RuntimeException("生成密钥对失败: NoSuchProviderException", e);
        }
    }

    /**
     * 加密明文
     * 
     * @param plainText 明文
     * @return 加密后的密文
     * @throws Exception 加密异常
     */
    public static String encrypt(String plainText) throws Exception {
        String encrypt = ConfigTools.encrypt(privateKey, plainText);
        System.out.println("原文: " + plainText);
        System.out.println("密文: " + encrypt);
        return encrypt;
    }

    /**
     * 解密密文
     * 
     * @param encryptedText 密文
     * @return 解密后的明文
     * @throws Exception 解密异常
     */
    public static String decrypt(String encryptedText) throws Exception {
        String decrypt = ConfigTools.decrypt(publicKey, encryptedText);
        System.out.println("密文: " + encryptedText);
        System.out.println("明文: " + decrypt);
        return decrypt;
    }

    /**
     * 获取公钥
     * 
     * @return 公钥
     */
    public static String getPublicKey() {
        return publicKey;
    }

    /**
     * 获取私钥
     * 
     * @return 私钥
     */
    public static String getPrivateKey() {
        return privateKey;
    }

    /**
     * 主方法，用于生成加密配置
     */
    public static void main(String[] args) {
        try {
            System.out.println("=== Druid 数据库连接信息加密工具 ===");
            System.out.println();
            
            // 需要加密的信息
            String dbUrl = "*******************************************************************************************************************************************************************************************************";
            String dbPassword = "Wu17740473227";
            String druidPassword = "123456";
            
            System.out.println("生成的密钥对:");
            System.out.println("私钥 (privateKey): " + getPrivateKey());
            System.out.println("公钥 (publicKey): " + getPublicKey());
            System.out.println();
            
            System.out.println("=== 加密结果 ===");
            
            // 加密数据库URL
            String encryptedUrl = encrypt(dbUrl);
            System.out.println("数据库URL加密结果:");
            System.out.println("原文: " + dbUrl);
            System.out.println("密文: " + encryptedUrl);
            System.out.println();
            
            // 加密数据库密码
            String encryptedDbPassword = encrypt(dbPassword);
            System.out.println("数据库密码加密结果:");
            System.out.println("原文: " + dbPassword);
            System.out.println("密文: " + encryptedDbPassword);
            System.out.println();
            
            // 加密Druid监控密码
            String encryptedDruidPassword = encrypt(druidPassword);
            System.out.println("Druid监控密码加密结果:");
            System.out.println("原文: " + druidPassword);
            System.out.println("密文: " + encryptedDruidPassword);
            System.out.println();
            
            System.out.println("=== 配置文件示例 ===");
            generateConfigExample(encryptedUrl, encryptedDbPassword, encryptedDruidPassword);
            
        } catch (Exception e) {
            System.err.println("加密过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 生成配置文件示例
     */
    private static void generateConfigExample(String encryptedUrl, String encryptedDbPassword, String encryptedDruidPassword) {
        System.out.println("请将以下配置复制到您的 application.yml 文件中:");
        System.out.println();
        System.out.println("spring:");
        System.out.println("  datasource:");
        System.out.println("    type: com.alibaba.druid.pool.DruidDataSource");
        System.out.println("    username: root");
        System.out.println("    password: " + encryptedDbPassword);
        System.out.println("    driver-class-name: com.mysql.cj.jdbc.Driver");
        System.out.println("    url: " + encryptedUrl);
        System.out.println("    druid:");
        System.out.println("      initial-size: 20");
        System.out.println("      min-idle: 20");
        System.out.println("      max-active: 100");
        System.out.println("      max-wait: 60000");
        System.out.println("      # 启用配置解密");
        System.out.println("      connection-properties: config.decrypt=true;config.decrypt.key=" + getPublicKey());
        System.out.println("      # 启用连接池过滤器");
        System.out.println("      filters: config");
        System.out.println("      stat-view-servlet:");
        System.out.println("        enabled: true");
        System.out.println("        url-pattern: /druid/*");
        System.out.println("        login-username: admin");
        System.out.println("        login-password: " + encryptedDruidPassword);
        System.out.println("      filter:");
        System.out.println("        stat:");
        System.out.println("          enabled: true");
        System.out.println("          slow-sql-millis: 2000");
        System.out.println("          log-slow-sql: true");
        System.out.println("        wall:");
        System.out.println("          enabled: true");
        System.out.println("        config:");
        System.out.println("          enabled: true");
        System.out.println("      validation-query: SELECT 1");
        System.out.println("      test-while-idle: true");
        System.out.println("      test-on-borrow: false");
        System.out.println("      test-on-return: false");
        System.out.println("      time-between-eviction-runs-millis: 60000");
        System.out.println("      min-evictable-idle-time-millis: 300000");
        System.out.println("      validation-query-timeout: 3");
    }
}
