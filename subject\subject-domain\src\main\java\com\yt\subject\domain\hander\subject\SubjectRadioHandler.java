package com.yt.subject.domain.hander.subject;

import com.alibaba.fastjson.JSON;
import com.yt.subject.commom.enums.IsDeletedFlagEnum;
import com.yt.subject.commom.enums.SubjectInfoEnums;
import com.yt.subject.domain.convert.RadioConverter;
import com.yt.subject.domain.entity.SubjectAnswerBo;
import com.yt.subject.domain.entity.SubjectInfoBo;
import com.yt.subject.domain.entity.SubjectOptionBo;
import com.yt.subject.infra.basic.entity.SubjectRadio;
import com.yt.subject.infra.basic.service.SubjectRadioService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.List;

@Component
@Slf4j
public class SubjectRadioHandler implements SubjectTypeHandler{

    @Resource
    private SubjectRadioService subjectRadioService;

    @Override
    public SubjectInfoEnums getSubjectType() {
        return SubjectInfoEnums.RADIO;
    }

    @Override
    public Boolean add(SubjectInfoBo subjectInfoBo) {
        if (log.isDebugEnabled()) {
            log.debug("SubjectInfoDomainServiceImpl.add.bo:{}", JSON.toJSONString(subjectInfoBo));
        }
        //单选题目的插入
        List<SubjectRadio> subjectRadioList = new LinkedList<>();
        subjectInfoBo.getOptionList().forEach(option -> {
            SubjectRadio subjectRadio = RadioConverter.INSTANCE.convertBoToEntity(option);
            subjectRadio.setSubjectId(subjectInfoBo.getId());
            subjectRadio.setIsDeleted(IsDeletedFlagEnum.UN_DELETED.getCode());
            subjectRadioList.add(subjectRadio);
        });
       return subjectRadioService.insertBatch(subjectRadioList);
    }
    @Override
    public SubjectOptionBo query(int subjectId) {
        SubjectRadio subjectRadio = new SubjectRadio();
        subjectRadio.setSubjectId(Long.valueOf(subjectId));
        List<SubjectRadio> result = subjectRadioService.queryByCondition(subjectRadio);
        List<SubjectAnswerBo> subjectAnswerBoList = RadioConverter.INSTANCE.convertEntityToBoList(result);
        SubjectOptionBo subjectOptionBo = new SubjectOptionBo();
        subjectOptionBo.setOptionList(subjectAnswerBoList);
        return subjectOptionBo;
    }
}
