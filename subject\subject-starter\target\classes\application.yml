server:
  port: 3000
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    username: root
    password: Wu147258.
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************************************************************
    druid:
      initial-size: 0
      min-idle: 0
      max-active: 3
      max-wait: 60000
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        login-username: admin
        login-password: 123456
      filter:
        stat:
          enabled: true
          slow-sql-millis: 2000
          log-slow-sql: true
        wall:
          enabled: true
      validation-query: SELECT 1
      test-while-idle: true
      test-on-return: false
      time-between-eviction-runs-millis: 30000
      min-evictable-idle-time-millis: 60000
      validation-query-timeout: 3
      # 连接泄漏检测 - 更激进的设置
      remove-abandoned: true
      remove-abandoned-timeout: 300
      log-abandoned: true
      # 连接池状态监控
      pool-prepared-statements: false
      max-pool-prepared-statement-per-connection-size: 5
      # 快速失败
      fail-fast: true
      # 连接有效性检查
      test-on-borrow: true
logging:
  config: classpath:log4j2-spring.xml
  level:
    # 关键配置：关闭MyBatis DAO接口的默认INFO日志，避免重复打印 "==> Preparing:" 等信息
    com.yt.subject.infra.basic.dao: WARN