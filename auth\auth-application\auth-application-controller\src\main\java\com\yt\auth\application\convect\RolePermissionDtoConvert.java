package com.yt.auth.application.convect;


import com.yt.auth.application.entity.AuthRolePermissionDto;
import com.yt.auth.domain.entity.AuthRolePermissionBo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface RolePermissionDtoConvert {
    RolePermissionDtoConvert INSTANCE = Mappers.getMapper(RolePermissionDtoConvert.class);
    AuthRolePermissionBo dtoToBo(AuthRolePermissionDto authRolePermissionDto);
}
