package com.yt.subject.application.convect;

import com.yt.subject.application.entity.SubjectCategoryDto;
import com.yt.subject.domain.entity.SubjectCategoryBo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface CategoryDtoConvert {
    CategoryDtoConvert INSTANCE = Mappers.getMapper(CategoryDtoConvert.class);
    SubjectCategoryBo dtoToBo(SubjectCategoryDto subjectCategoryDto);
    List<SubjectCategoryDto> boToDtoList(List<SubjectCategoryBo> subjectCategoryBoList);
    SubjectCategoryDto boToDto(SubjectCategoryBo subjectCategoryBoList);
}
