package com.yt.subject.infra.basic.service.impl;

import com.yt.subject.infra.basic.dao.SubjectLabelDao;
import com.yt.subject.infra.basic.entity.SubjectLabel;
import com.yt.subject.infra.basic.service.SubjectLabelService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 题目标签表(SubjectLabel)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-11 12:44:19
 */
@Service("subjectLabelService")
public class SubjectLabelServiceImpl implements SubjectLabelService {
    @Resource
    private SubjectLabelDao subjectLabelDao;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public SubjectLabel queryById(Long id) {
        return this.subjectLabelDao.queryById(id);
    }

    /**
     * 分页查询
     *
     * @param subjectLabel 筛选条件
     * @param pageRequest      分页对象
     * @return 查询结果
     */
    @Override
    public Page<SubjectLabel> queryByPage(SubjectLabel subjectLabel, PageRequest pageRequest) {
        long total = this.subjectLabelDao.count(subjectLabel);
        return new PageImpl<>(this.subjectLabelDao.queryAllByLimit(subjectLabel, pageRequest), pageRequest, total);
    }

    /**
     * 新增数据
     *
     * @param subjectLabel 实例对象
     * @return 实例对象
     */
    @Override
    public Boolean insert(SubjectLabel subjectLabel) {
        return subjectLabelDao.insert(subjectLabel) >0;
    }

    /**
     * 修改数据
     *
     * @param subjectLabel 实例对象
     * @return 实例对象
     */
    @Override
    public Boolean update(SubjectLabel subjectLabel) {
        return subjectLabelDao.update(subjectLabel)>0;
    }


    @Override
    public List<SubjectLabel> batchQueryById(List<Long> labelIdList) {
        return this.subjectLabelDao.batchQueryById(labelIdList);
    }

    @Override
    public List<SubjectLabel> queryByCondition(SubjectLabel subjectLabel) {
        return this.subjectLabelDao.queryByCondition(subjectLabel);
    }
}
