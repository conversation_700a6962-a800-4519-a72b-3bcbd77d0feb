package com.yt.subject.infra.basic.service;

import com.yt.subject.infra.basic.entity.SubjectRadio;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.List;

/**
 * 单选题信息表(SubjectRadio)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-11 12:45:35
 */
public interface SubjectRadioService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    SubjectRadio queryById(Long id);

    /**
     * 分页查询
     *
     * @param subjectRadio 筛选条件
     * @param pageRequest      分页对象
     * @return 查询结果
     */
    Page<SubjectRadio> queryByPage(SubjectRadio subjectRadio, PageRequest pageRequest);

    /**
     * 新增数据
     *
     * @param subjectRadio 实例对象
     * @return 实例对象
     */
    SubjectRadio insert(SubjectRadio subjectRadio);

    /**
     * 修改数据
     *
     * @param subjectRadio 实例对象
     * @return 实例对象
     */
    SubjectRadio update(SubjectRadio subjectRadio);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(Long id);

    boolean insertBatch(List<SubjectRadio> radioList);

    List<SubjectRadio> queryByCondition(SubjectRadio subjectRadio);
}
