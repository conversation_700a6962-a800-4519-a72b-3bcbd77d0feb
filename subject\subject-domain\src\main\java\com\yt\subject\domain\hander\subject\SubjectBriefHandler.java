package com.yt.subject.domain.hander.subject;

import com.alibaba.fastjson.JSON;
import com.yt.subject.commom.enums.IsDeletedFlagEnum;
import com.yt.subject.commom.enums.SubjectInfoEnums;
import com.yt.subject.domain.convert.BriefConverter;
import com.yt.subject.domain.entity.SubjectInfoBo;
import com.yt.subject.domain.entity.SubjectOptionBo;
import com.yt.subject.infra.basic.entity.SubjectBrief;
import com.yt.subject.infra.basic.service.SubjectBriefService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class SubjectBriefHandler implements SubjectTypeHandler {

    @Resource
    private SubjectBriefService subjectBriefService;

    @Override
    public SubjectInfoEnums getSubjectType() {
        return SubjectInfoEnums.BRIEF;
    }

    @Override
    public Boolean add(SubjectInfoBo subjectInfoBo) {
        if (log.isDebugEnabled()) {
            log.debug("SubjectInfoDomainServiceImpl.add.bo:{}", JSON.toJSONString(subjectInfoBo));
        }
        SubjectBrief subjectBrief = BriefConverter.INSTANCE.convertBoToEntity(subjectInfoBo);
        subjectBrief.setSubjectId(subjectInfoBo.getId().intValue());
        subjectBrief.setIsDeleted(IsDeletedFlagEnum.UN_DELETED.getCode());
        return subjectBriefService.insert(subjectBrief);
    }

    @Override
    public SubjectOptionBo query(int subjectId) {
        SubjectBrief subjectBrief = new SubjectBrief();
        subjectBrief.setSubjectId(subjectId);
        SubjectBrief result = subjectBriefService.queryByCondition(subjectBrief);
        SubjectOptionBo subjectOptionBO = new SubjectOptionBo();
        subjectOptionBO.setSubjectAnswer(result.getSubjectAnswer());
        return subjectOptionBO;
    }
}
