package com.yt.auth.domain.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * 用户角色表(AuthUserRole)实体类
 *
 * <AUTHOR>
 * @since 2025-07-17 17:04:35
 */
@Data
public class AuthUserRoleBo implements Serializable {
    private static final long serialVersionUID = -33859170634386359L;
/**
     * 主键
     */
    private Long id;
/**
     * 用户id
     */
    private Long userId;
/**
     * 角色id
     */
    private Long roleId;

    private Integer isDeleted;


}

