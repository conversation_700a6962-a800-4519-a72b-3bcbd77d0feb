package com.yt.subject.infra.basic.service;

import com.yt.subject.infra.basic.entity.SubjectInfo;

import java.util.List;

/**
 * 题目信息表(SubjectInfo)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-11 12:43:38
 */

public interface SubjectInfoService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    SubjectInfo queryById(Long id);



    /**
     * 新增数据
     *
     * @param subjectInfo 实例对象
     * @return 实例对象
     */
    SubjectInfo insert(SubjectInfo subjectInfo);

    /**
     * 修改数据
     *
     * @param subjectInfo 实例对象
     * @return 实例对象
     */
    SubjectInfo update(SubjectInfo subjectInfo);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(Long id);

    int countInfo(SubjectInfo subjectInfo, Long categoryId, Long labelId);

    List<SubjectInfo> queryByPage(SubjectInfo subjectInfo, Long categoryId, Long labelId, int start, Integer pageSize);
}
