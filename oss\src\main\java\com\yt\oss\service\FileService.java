package com.yt.oss.service;

import com.yt.oss.utils.StorageAdapter;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import javax.annotation.Resource;
import java.util.List;

@Service
public class FileService {

    @Resource
    private StorageAdapter storageAdapter;

    public FileService(StorageAdapter storageAdapter) {
        this.storageAdapter = storageAdapter;
    }

    public String uploadFile(MultipartFile uploadFile, String bucket, String objectName) {
        String originalFilename = uploadFile.getOriginalFilename();
        String finalObjectName = objectName + "/" + originalFilename;
        return storageAdapter.uploadFile(uploadFile, bucket, finalObjectName);
    }

    public List<String> listBuckets() {
        return storageAdapter.listBuckets();
    }

    /**
     * 新增文件下载方法
     */
    public byte[] downloadFileAsBytes(String bucket, String objectName) {
        return storageAdapter.downloadFileAsBytes(objectName, bucket);
    }


   //获取文件的预签名URL
   public String getPresignedObjectUrl(String bucket, String objectName) {
       return storageAdapter.getPresignedObjectUrl(bucket, objectName);
   }
}
