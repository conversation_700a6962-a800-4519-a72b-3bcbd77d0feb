package com.yt.auth.domain.convert;

import com.yt.auth.domain.entity.AuthUserRoleBo;
import com.yt.auth.infra.basic.entity.AuthUserRole;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-29T01:01:20+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
public class UserRoleBoConvertImpl implements UserRoleBoConvert {

    @Override
    public AuthUserRole boToy(AuthUserRoleBo authRolePermissionBo) {
        if ( authRolePermissionBo == null ) {
            return null;
        }

        AuthUserRole authUserRole = new AuthUserRole();

        authUserRole.setId( authRolePermissionBo.getId() );
        authUserRole.setUserId( authRolePermissionBo.getUserId() );
        authUserRole.setRoleId( authRolePermissionBo.getRoleId() );
        authUserRole.setIsDeleted( authRolePermissionBo.getIsDeleted() );

        return authUserRole;
    }
}
