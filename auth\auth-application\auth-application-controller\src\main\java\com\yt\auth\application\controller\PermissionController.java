package com.yt.auth.application.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.google.common.base.Preconditions;
import com.yt.auth.application.convect.PermissionDtoConvert;
import com.yt.auth.application.entity.AuthPermissionDto;
import com.yt.auth.domain.entity.AuthPermissionBo;
import com.yt.auth.domain.service.AuthPermissionDomainService;
import com.yt.auth.entity.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/permission")
@Slf4j
public class PermissionController {

    @Resource
    private AuthPermissionDomainService authPermissionDomainService;


    /**
     *  新增权限
     */
    @RequestMapping("/add")
    public Result<Boolean> add(@RequestBody AuthPermissionDto authPermissionDto){
        try {
            if(log.isDebugEnabled())
                log.info("PermissionController.add.controller:{}", JSON.toJSONString(authPermissionDto));
            Preconditions.checkArgument(!StringUtils.isBlank(authPermissionDto.getName()), "名称不能为空");
            AuthPermissionBo authPermissionBo = PermissionDtoConvert.INSTANCE.dtoToBo(authPermissionDto);
            return Result.success(authPermissionDomainService.add(authPermissionBo));
        }catch (Exception e){
            log.error("PermissionController.add.error:{}", e.getMessage(),e);
            return Result.error("新增权限失败");
        }
    }


    /**
     *  修改权限
     */
    @RequestMapping("/update")
    public Result<Boolean> update(@RequestBody AuthPermissionDto authPermissionDto){
        try {
            if(log.isDebugEnabled())
                log.info("PermissionController.update.controller:{}", JSON.toJSONString(authPermissionDto));
            Preconditions.checkNotNull(authPermissionDto.getId(),"权限id不能为空");
            AuthPermissionBo authPermissionBo = PermissionDtoConvert.INSTANCE.dtoToBo(authPermissionDto);
            return Result.success(authPermissionDomainService.update(authPermissionBo));
        }catch (Exception e){
            log.error("PermissionController.update.error:{}", e.getMessage(),e);
            return Result.error("更新权限失败");
        }
    }


    /**
     *  删除权限
     */
    @RequestMapping("/delete")
    public Result<Boolean> delete(@RequestBody AuthPermissionDto authPermissionDto){
        try {
            if(log.isDebugEnabled())
                log.info("PermissionController.delete.controller:{}", JSON.toJSONString(authPermissionDto));
            Preconditions.checkNotNull(authPermissionDto.getId(),"权限id不能为空");
            AuthPermissionBo authPermissionBo = PermissionDtoConvert.INSTANCE.dtoToBo(authPermissionDto);
            return Result.success(authPermissionDomainService.delete(authPermissionBo));
        }catch (Exception e){
            log.error("PermissionController.delete.error:{}", e.getMessage(),e);
            return Result.error("删除权限失败");
        }
    }

    /**
     *  删除权限
     */
    @RequestMapping("/getPermission")
    public Result<List<String>> getPermission(String userName){
        try {
            if(log.isDebugEnabled())
                log.info("PermissionController.getPermission.controller:{}",userName );
            Preconditions.checkArgument(!StringUtils .isBlank(userName),"用户id不能为空");
            return Result.success(authPermissionDomainService.getPermission(userName));
        }catch (Exception e){
            log.error("PermissionController.getPermission.error:{}", e.getMessage(),e);
            return Result.error("删除权限失败");
        }
    }

}
