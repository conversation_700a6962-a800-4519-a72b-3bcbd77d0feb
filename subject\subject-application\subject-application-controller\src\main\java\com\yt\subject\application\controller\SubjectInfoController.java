package com.yt.subject.application.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Preconditions;
import com.yt.subject.application.convect.AnswerDtoConvert;
import com.yt.subject.application.convect.SubjectDtoConvert;
import com.yt.subject.application.entity.SubjectInfoDto;
import com.yt.subject.commom.entity.PageResult;
import com.yt.subject.commom.entity.Result;
import com.yt.subject.domain.entity.SubjectAnswerBo;
import com.yt.subject.domain.entity.SubjectInfoBo;
import com.yt.subject.domain.service.SubjectInfoDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;


/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/subject")
@Slf4j
public class SubjectInfoController {

    @Resource
    private SubjectInfoDomainService subjectInfoDomainService;

    @RequestMapping("/add")
    public Result<Object> add(@RequestBody SubjectInfoDto subjectInfoDto) {
        try {
            if(log.isDebugEnabled()){
                log.info("SubjectInfoController.add.dto:{}", JSON.toJSONString(subjectInfoDto));
            }
            Preconditions.checkNotNull(subjectInfoDto.getSubjectDifficult(),"题目难度不能为空");
            Preconditions.checkNotNull(subjectInfoDto.getSubjectType(),"题目类型不能为空");
            Preconditions.checkNotNull(subjectInfoDto.getSubjectScore(),"题目分数不能为空");
            Preconditions.checkArgument(!CollectionUtils.isEmpty(subjectInfoDto.getCategoryIds()),"分类id不能为空");
            Preconditions.checkArgument(!CollectionUtils.isEmpty(subjectInfoDto.getLabelIds()),"标签id不能为空");
            SubjectInfoBo subjectInfoBo = SubjectDtoConvert.INSTANCE.dtoToBo(subjectInfoDto);
            List<SubjectAnswerBo> subjectAnswerDtos = AnswerDtoConvert.INSTANCE.dtoToBo(subjectInfoDto.getOptionList());
            subjectInfoBo.setOptionList(subjectAnswerDtos);
            Boolean rs=subjectInfoDomainService.add(subjectInfoBo);
            return Result.success(rs);
        } catch (Exception e) {
            log.error("SubjectInfoController.add.error:{}", e.getMessage(), e);
            return Result.error("新增失败");
        }
    }

    @RequestMapping("/getSubjectPage")
    public Result<PageResult<SubjectInfoBo>> getSubjectPage(@RequestBody SubjectInfoDto subjectInfoDto) {
        try {
            if(log.isDebugEnabled()){
                log.info("SubjectInfoController.getSubjectPage.dto:{}", JSON.toJSONString(subjectInfoDto));
            }
            Preconditions.checkNotNull(subjectInfoDto.getLabelId(),"标签id不能为空");
            Preconditions.checkNotNull(subjectInfoDto.getCategoryId(),"分类id不能为空");
            SubjectInfoBo subjectInfoBo = SubjectDtoConvert.INSTANCE.dtoToBo(subjectInfoDto);
            PageResult<SubjectInfoBo> infoPageResult=subjectInfoDomainService.getSubjectPage(subjectInfoBo);
            return Result.success(infoPageResult);
        } catch (Exception e) {
            log.error("SubjectInfoController.getSubjectPage.error:{}", e.getMessage(), e);
            return Result.error("新增失败");
        }
    }

    @RequestMapping("/querySubjectInfo")
    public Result<Object> querySubjectInfo(@RequestBody SubjectInfoDto subjectInfoDto) {
        try {
            if(log.isDebugEnabled()){
                log.info("SubjectInfoController.querySubjectInfo.dto:{}", JSON.toJSONString(subjectInfoDto));
            }
            SubjectInfoBo subjectInfoBo = SubjectDtoConvert.INSTANCE.dtoToBo(subjectInfoDto);
            SubjectInfoBo rs=subjectInfoDomainService.querySubjectInfo(subjectInfoBo);
            return Result.success(rs);
        } catch (Exception e) {
            log.error("SubjectInfoController.querySubjectInfo.error:{}", e.getMessage(), e);
            return Result.error("新增失败");
        }
    }

}
