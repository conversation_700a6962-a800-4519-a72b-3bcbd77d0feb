<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yt.auth.infra.basic.dao.AuthRoleDao">

    <resultMap type="com.yt.auth.infra.basic.entity.AuthRole" id="AuthRoleMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="roleName" column="role_name" jdbcType="VARCHAR"/>
        <result property="roleKey" column="role_key" jdbcType="VARCHAR"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="AuthRoleMap">
        select
id, role_name, role_key, created_by, created_time, update_by, update_time, is_deleted
        from auth_role
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="AuthRoleMap">
        select
id, role_name, role_key, created_by, created_time, update_by, update_time, is_deleted
        from auth_role
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="roleName != null and roleName != ''">
                and role_name = #{roleName}
            </if>
            <if test="roleKey != null and roleKey != ''">
                and role_key = #{roleKey}
            </if>
            <if test="createdBy != null and createdBy != ''">
                and created_by = #{createdBy}
            </if>
            <if test="createdTime != null">
                and created_time = #{createdTime}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and update_by = #{updateBy}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="isDeleted != null">
                and is_deleted = #{isDeleted}
            </if>
        </where>
        limit #{pageable.offset}, #{pageable.pageSize}
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from auth_role
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="roleName != null and roleName != ''">
                and role_name = #{roleName}
            </if>
            <if test="roleKey != null and roleKey != ''">
                and role_key = #{roleKey}
            </if>
            <if test="createdBy != null and createdBy != ''">
                and created_by = #{createdBy}
            </if>
            <if test="createdTime != null">
                and created_time = #{createdTime}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and update_by = #{updateBy}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="isDeleted != null">
                and is_deleted = #{isDeleted}
            </if>
        </where>
    </select>
    <select id="queryByIds" resultMap="AuthRoleMap">
        select *
        from auth_role
        where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and is_deleted = 0
    </select>

    <select id="queryByCondition" resultMap="AuthRoleMap">
        select *
        from auth_role
            <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="roleName != null and roleName != ''">
                and role_name = #{roleName}
            </if>
            <if test="roleKey != null and roleKey != ''">
                and role_key = #{roleKey}
            </if>
            <if test="createdBy != null and createdBy != ''">
                and created_by = #{createdBy}
            </if>
            <if test="createdTime != null">
                and created_time = #{createdTime}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and update_by = #{updateBy}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="isDeleted != null">
                and is_deleted = 0
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into auth_role(role_name, role_key, created_by, created_time, update_by, update_time, is_deleted)
        values (#{roleName}, #{roleKey}, #{createdBy}, #{createdTime}, #{updateBy}, #{updateTime}, #{isDeleted})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into auth_role(role_name, role_key, created_by, created_time, update_by, update_time, is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.roleName}, #{entity.roleKey}, #{entity.createdBy}, #{entity.createdTime}, #{entity.updateBy}, #{entity.updateTime}, #{entity.isDeleted})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into auth_role(role_name, role_key, created_by, created_time, update_by, update_time, is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.roleName}, #{entity.roleKey}, #{entity.createdBy}, #{entity.createdTime}, #{entity.updateBy}, #{entity.updateTime}, #{entity.isDeleted})
        </foreach>
        on duplicate key update
role_name = values(role_name),
role_key = values(role_key),
created_by = values(created_by),
created_time = values(created_time),
update_by = values(update_by),
update_time = values(update_time),
is_deleted = values(is_deleted)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update auth_role
        <set>
            <if test="roleName != null and roleName != ''">
                role_name = #{roleName},
            </if>
            <if test="roleKey != null and roleKey != ''">
                role_key = #{roleKey},
            </if>
            <if test="createdBy != null and createdBy != ''">
                created_by = #{createdBy},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from auth_role where id = #{id}
    </delete>

</mapper>

