package com.yt.auth.domain.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 角色权限关联表(AuthRolePermission)实体类
 *
 * <AUTHOR>
 * @since 2025-07-17 17:04:41
 */
@Data
public class AuthRolePermissionBo implements Serializable {
    private static final long serialVersionUID = -96722357587031435L;

    private Long id;
/**
     * 角色id
     */
    private Long roleId;
/**
     * 权限id
     */
    private Long permissionId;

    private Integer isDeleted;

        private List<Long> permissionIdList;

}

