package com.yt.subject.application.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 题目分类(SubjectCategory)实体类
 *
 * <AUTHOR>
 * @since 2025-07-11 12:42:30
 */
@Data
public class SubjectCategoryDto implements Serializable {
    private static final long serialVersionUID = 869822536649368838L;
    /**
     * 主键
     */
    private Long id;
    /**
     * 分类名称
     */
    private String categoryName;
    /**
     * 分类类型
     */
    private Integer categoryType;
    /**
     * 图标连接
     */
    private String imageUrl;
    /**
     * 父级id
     */
    private Long parentId;

    private Integer count;

    private List<SubjectLabelDto> labelDTOList;
}
