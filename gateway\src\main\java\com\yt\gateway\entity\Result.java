package com.yt.gateway.entity;

import com.yt.gateway.enums.ResultCodeEnum;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class Result<T> {

    private Boolean success;

    private Integer code;

    private String message;

    private T data;


    public static Result<Void> success() {
        return success(null);
    }

    public static <T> Result<T> success(T data){
        Result<T> result = new Result<>();
        result.setSuccess(true);
        result.setCode(ResultCodeEnum.SUCCESS.getCode());
        result.setMessage(ResultCodeEnum.SUCCESS.getDesc());
        result.setData(data);
        return result;
    }

    public static Result<Void> error(){
        return error(null);
    }

    public static <T> Result<T> error(T data){
        Result<T> result = new Result<>();
        result.setSuccess(false);
        result.setCode(ResultCodeEnum.FAIL.getCode());
        result.setMessage(ResultCodeEnum.FAIL.getDesc());
        result.setData(data);
        return result;
    }

    public static <T> Result<T> error(String message){
        Result<T> result = new Result<>();
        result.setSuccess(false);
        result.setCode(ResultCodeEnum.FAIL.getCode());
        result.setMessage(message);
        result.setData(null);
        return result;
    }


    public static Result error(Integer code, String message) {
        Result result = new Result<>();
        result.setSuccess(false);
        result.setCode(code);
        result.setMessage(message);
        return result;
    }
}