package com.yt.subject.commom.enums;

import lombok.Getter;
/**
 * <AUTHOR>
 */
@Getter
public enum SubjectInfoEnums {
    RADIO(1,"单选"),
    MULTIPLE(2,"多选"),
    JUDGE(3,"判断"),
    BRIEF(4,"简答");

    private final int code;
    private final String desc;

    SubjectInfoEnums(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    public static SubjectInfoEnums getByCode(int codeVal){
        for (SubjectInfoEnums resultCodeEnum : SubjectInfoEnums.values()){
            if (resultCodeEnum.code==codeVal){
                return resultCodeEnum;
            }
        }
        return null;
    }

}
