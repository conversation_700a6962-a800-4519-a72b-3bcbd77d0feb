package com.yt.subject.domain.service;

import com.yt.subject.domain.entity.SubjectCategoryBo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SubjectCategoryDomainService {

    void add(SubjectCategoryBo subjectCategoryBo);

    List<SubjectCategoryBo> queryCategory(SubjectCategoryBo subjectCategoryBo);

    Boolean update(SubjectCategoryBo subjectCategoryBo);

    Boolean delete(SubjectCategoryBo subjectCategoryBo);

    List<SubjectCategoryBo> queryCategoryAndLabel(SubjectCategoryBo subjectCategoryBO);
}
