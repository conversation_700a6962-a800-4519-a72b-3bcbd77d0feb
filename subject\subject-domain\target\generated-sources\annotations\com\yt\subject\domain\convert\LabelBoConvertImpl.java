package com.yt.subject.domain.convert;

import com.yt.subject.domain.entity.SubjectLabelBo;
import com.yt.subject.infra.basic.entity.SubjectLabel;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-29T01:01:28+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
public class LabelBoConvertImpl implements LabelBoConvert {

    @Override
    public SubjectLabel boToY(SubjectLabelBo subjectLabelBo) {
        if ( subjectLabelBo == null ) {
            return null;
        }

        SubjectLabel subjectLabel = new SubjectLabel();

        subjectLabel.setId( subjectLabelBo.getId() );
        subjectLabel.setLabelName( subjectLabelBo.getLabelName() );
        subjectLabel.setSortNum( subjectLabelBo.getSortNum() );
        subjectLabel.setCategoryId( subjectLabelBo.getCategoryId() );
        subjectLabel.setIsDeleted( subjectLabelBo.getIsDeleted() );

        return subjectLabel;
    }

    @Override
    public List<SubjectLabelBo> yToBo(List<SubjectLabel> subjectLabel) {
        if ( subjectLabel == null ) {
            return null;
        }

        List<SubjectLabelBo> list = new ArrayList<SubjectLabelBo>( subjectLabel.size() );
        for ( SubjectLabel subjectLabel1 : subjectLabel ) {
            list.add( subjectLabelToSubjectLabelBo( subjectLabel1 ) );
        }

        return list;
    }

    protected SubjectLabelBo subjectLabelToSubjectLabelBo(SubjectLabel subjectLabel) {
        if ( subjectLabel == null ) {
            return null;
        }

        SubjectLabelBo subjectLabelBo = new SubjectLabelBo();

        subjectLabelBo.setId( subjectLabel.getId() );
        subjectLabelBo.setLabelName( subjectLabel.getLabelName() );
        subjectLabelBo.setSortNum( subjectLabel.getSortNum() );
        subjectLabelBo.setCategoryId( subjectLabel.getCategoryId() );
        subjectLabelBo.setIsDeleted( subjectLabel.getIsDeleted() );

        return subjectLabelBo;
    }
}
