package com.yt.subject.infra.basic.service.impl;

import com.yt.subject.infra.basic.dao.SubjectCategoryDao;
import com.yt.subject.infra.basic.entity.SubjectCategory;
import com.yt.subject.infra.basic.service.SubjectCategoryService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 题目分类(SubjectCategory)表服务实现类
 * <AUTHOR>
 */
@Service("subjectCategoryService")
public class SubjectCategoryServiceImpl implements SubjectCategoryService {
    @Resource
    private SubjectCategoryDao subjectCategoryDao;

    /**
     * 通过ID查询单条数据
     */
    @Override
    public SubjectCategory queryById(Long id) {
        return this.subjectCategoryDao.queryById(id);
    }

    @Override
    public Page<SubjectCategory> queryByPage(SubjectCategory subjectCategory, PageRequest pageRequest) {
        return null;
    }


    /**
     * 新增数据
     */
    @Override
    public SubjectCategory insert(SubjectCategory subjectCategory) {
        this.subjectCategoryDao.insert(subjectCategory);
        return subjectCategory;
    }

    /**
     * 修改数据
     */
    @Override
    public Boolean update(SubjectCategory subjectCategory) {
        return subjectCategoryDao.update(subjectCategory)>0;
    }

    @Override
    public List<SubjectCategory> queryCategory(SubjectCategory subjectCategory) {
        return this.subjectCategoryDao.queryCategory(subjectCategory);
    }

    @Override
    public Integer queryCount(Long id) {
        return subjectCategoryDao.count(id);
    }
}
