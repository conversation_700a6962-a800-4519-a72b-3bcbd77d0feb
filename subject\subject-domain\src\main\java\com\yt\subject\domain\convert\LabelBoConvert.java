package com.yt.subject.domain.convert;

import com.yt.subject.domain.entity.SubjectLabelBo;
import com.yt.subject.infra.basic.entity.SubjectLabel;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface LabelBoConvert {
    LabelBoConvert INSTANCE = Mappers.getMapper(LabelBoConvert.class);
    SubjectLabel boToY(SubjectLabelBo subjectLabelBo);
    List<SubjectLabelBo> yToBo(List<SubjectLabel> subjectLabel);

}
