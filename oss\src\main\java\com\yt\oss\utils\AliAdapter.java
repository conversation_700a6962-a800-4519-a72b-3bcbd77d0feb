package com.yt.oss.utils;

import cn.hutool.core.io.IoUtil;
import com.aliyun.oss.HttpMethod;
import com.aliyun.oss.OSS;
import com.aliyun.oss.model.Bucket;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.InputStream;
import java.net.URL;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 阿里云oss操作工具类
 * <AUTHOR>
 */

@Slf4j
public class AliAdapter implements StorageAdapter {

    @Resource
    private OSS ossClient;

    @Value("${AliOSS.endpoint}")
    private String endpoint;

    /**
     * 列出所有存储桶的存储桶信息
     */
    @Override
    public List<String> listBuckets() {
        List<String> result = new ArrayList<>();
        try {
            List<Bucket> buckets = ossClient.listBuckets();
            result = buckets.stream().map(Bucket::getName).collect(Collectors.toList());
        } catch (Exception e) {
            log.info("查询所有桶的错误信息:",e);
        }
        return result;

    }

    /**
     * (新增) 实现以字节数组下载文件
     */
    @Override
    public byte[] downloadFileAsBytes(String fileName, String bucketName) {
        try (InputStream inputStream = ossClient.getObject(bucketName, fileName).getObjectContent()) {
            return IoUtil.readBytes(inputStream);
        } catch (Exception e) {
            log.error("AliOSS文件下载错误: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * (新增) 为阿里云OSS实现获取预签名URL的方法
     */
    @Override
    public String getPresignedObjectUrl(String bucketName, String objectName) {
        try {
            Date expiration = new Date(new Date().getTime() + 3600 * 1000);
            URL url = ossClient.generatePresignedUrl(bucketName, objectName, expiration, HttpMethod.GET);
            return url.toString();
        } catch (Exception e) {
            log.error("AliOSS 文件预览错误: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 文件上传
     */
    @Override
    public String uploadFile(MultipartFile uploadFile, String bucket, String objectName) {
        try {
            if (!ossClient.doesBucketExist(bucket)) {
                ossClient.createBucket(bucket);
                log.info("Bucket {}", bucket);
            }
            InputStream inputStream = uploadFile.getInputStream();
            ossClient.putObject(bucket, objectName, inputStream);
            String urlEndpoint = endpoint.replaceFirst("https://", "").replaceFirst("http://", "");
            String url = "https://" + bucket + "." + urlEndpoint + "/" + objectName;
            log.info("文件上传成功, URL: {}", url);
            return url;

        } catch (Exception e) {
            log.error("文件上传失败 '{}'", bucket, e);
            return null;
        }
    }
}

