package com.yt.subject.application.convect;

import com.yt.subject.application.entity.SubjectLabelDto;
import com.yt.subject.domain.entity.SubjectLabelBo;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-29T01:01:30+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
public class LabelDtoConvertImpl implements LabelDtoConvert {

    @Override
    public SubjectLabelBo dtoToBo(SubjectLabelDto subjectLabelDto) {
        if ( subjectLabelDto == null ) {
            return null;
        }

        SubjectLabelBo subjectLabelBo = new SubjectLabelBo();

        subjectLabelBo.setId( subjectLabelDto.getId() );
        subjectLabelBo.setLabelName( subjectLabelDto.getLabelName() );
        subjectLabelBo.setSortNum( subjectLabelDto.getSortNum() );
        subjectLabelBo.setCategoryId( subjectLabelDto.getCategoryId() );
        subjectLabelBo.setIsDeleted( subjectLabelDto.getIsDeleted() );

        return subjectLabelBo;
    }

    @Override
    public List<SubjectLabelDto> boToDto(List<SubjectLabelBo> subjectLabelBoList) {
        if ( subjectLabelBoList == null ) {
            return null;
        }

        List<SubjectLabelDto> list = new ArrayList<SubjectLabelDto>( subjectLabelBoList.size() );
        for ( SubjectLabelBo subjectLabelBo : subjectLabelBoList ) {
            list.add( subjectLabelBoToSubjectLabelDto( subjectLabelBo ) );
        }

        return list;
    }

    protected SubjectLabelDto subjectLabelBoToSubjectLabelDto(SubjectLabelBo subjectLabelBo) {
        if ( subjectLabelBo == null ) {
            return null;
        }

        SubjectLabelDto subjectLabelDto = new SubjectLabelDto();

        subjectLabelDto.setId( subjectLabelBo.getId() );
        subjectLabelDto.setLabelName( subjectLabelBo.getLabelName() );
        subjectLabelDto.setSortNum( subjectLabelBo.getSortNum() );
        subjectLabelDto.setCategoryId( subjectLabelBo.getCategoryId() );
        subjectLabelDto.setIsDeleted( subjectLabelBo.getIsDeleted() );

        return subjectLabelDto;
    }
}
