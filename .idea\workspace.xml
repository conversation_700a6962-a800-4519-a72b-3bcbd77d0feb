<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="08e93c48-eb69-4e75-b7f3-976d4a8059ad" name="更改" comment="一期完成" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="application.yml" />
        <option value="Class" />
        <option value="Interface" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="PUSH_AUTO_UPDATE" value="true" />
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\Maven" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\Maven\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectCodeStyleSettingsMigration">
    <option name="version" value="2" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="2zyxcUlIyO9BmMFi7KQ3KMzAsov" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
    <option name="sortKey" value="BY_TIME_ASCENDING" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "HTTP 请求.generated-requests | #6.executor": "Run",
    "Maven.auth [clean].executor": "Run",
    "Maven.auth [install].executor": "Run",
    "Maven.auth-api [clean].executor": "Run",
    "Maven.gateway [clean].executor": "Run",
    "Maven.oss [clean].executor": "Run",
    "Maven.subject [clean].executor": "Run",
    "Maven.wx [clean].executor": "Run",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "SHARE_PROJECT_CONFIGURATION_FILES": "true",
    "Spring Boot.AuthApplication.executor": "Run",
    "Spring Boot.GatewayApplication.executor": "Debug",
    "Spring Boot.OSSApplication.executor": "Run",
    "Spring Boot.SubjectApplication.executor": "Run",
    "Spring Boot.WxApplication.executor": "Run",
    "git-widget-placeholder": "master",
    "ignore.virus.scanning.warn.message": "true",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "D:/ideaBook/club",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "onboarding.tips.debug.path": "B:/ideaBook/club/wx/src/main/java/com/yt/Main.java",
    "project.structure.last.edited": "项目",
    "project.structure.proportion": "0.15",
    "project.structure.side.proportion": "0.37356323",
    "run.configurations.included.in.services": "true",
    "service.view.auto.scroll.from.source": "true",
    "service.view.auto.scroll.to.source": "true",
    "settings.editor.selected.configurable": "project.propVCSSupport.DirectoryMappings",
    "vue.rearranger.settings.migration": "true"
  },
  "keyToStringList": {
    "DatabaseDriversLRU": [
      "mysql"
    ]
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CreateClassDialog.RecentsKey">
      <recent name="com.yt.auth.application.controller" />
      <recent name="com.yt.auth.domain.service" />
    </key>
    <key name="CopyFile.RECENT_KEYS">
      <recent name="B:\ideaBook\club\gateway\src\main\java\com\yt\gateway\entity" />
      <recent name="B:\ideaBook\club\gateway\src\main\java\com\yt\gateway" />
      <recent name="B:\ideaBook\club\auth\auth-common\src\main\java\com\yt\auth\commom" />
      <recent name="B:\ideaBook\club\auth\auth-domain\src\main\java\com\yt\auth\domain" />
      <recent name="B:\ideaBook\club\auth\auth-common\src\main\java\com\yt\auth\commom\enums" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.yt.wx.config" />
      <recent name="com.yt.wx.utils" />
      <recent name="com.yt.auth.domain.convert" />
      <recent name="com.yt.auth.domain.entity" />
    </key>
  </component>
  <component name="RedisHelper">
    <option name="connections">
      <list>
        <ConnectionInfo>
          <option name="global" value="false" />
          <option name="id" value="4b167219-506b-46f1-80e3-33d1deadc7b7" />
          <option name="name" value="club" />
          <option name="port" value="6379" />
          <option name="url" value="117.72.127.194" />
        </ConnectionInfo>
      </list>
    </option>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="KtorApplicationConfigurationType" />
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
    <option name="excludedTypes">
      <set>
        <option value="HttpClient.HttpRequestRunConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.GatewayApplication">
    <configuration name="generated-requests | #6" type="HttpClient.HttpRequestRunConfigurationType" factoryName="HTTP Request" temporary="true" nameIsGenerated="true" path="$APPLICATION_CONFIG_DIR$/scratches/generated-requests.http" index="6" runType="运行单个请求">
      <method v="2" />
    </configuration>
    <configuration name="AuthApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="auth-starter" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.yt.auth.AuthApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.yt.auth.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="GatewayApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="gateway" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.yt.gateway.GatewayApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="OSSApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="oss" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.yt.oss.OSSApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SubjectApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="subject-starter" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.yt.subject.SubjectApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="WxApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="wx" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.yt.wx.WxApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.yt.wx.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="HTTP 请求.generated-requests | #6" />
      <item itemvalue="Spring Boot.GatewayApplication" />
      <item itemvalue="Spring Boot.OSSApplication" />
      <item itemvalue="Spring Boot.SubjectApplication" />
      <item itemvalue="Spring Boot.AuthApplication" />
      <item itemvalue="Spring Boot.WxApplication" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Spring Boot.AuthApplication" />
        <item itemvalue="Spring Boot.WxApplication" />
        <item itemvalue="HTTP 请求.generated-requests | #6" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9f38398b9061-39b83d9b5494-intellij.indexing.shared.core-IU-241.19416.15" />
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-IU-241.19416.15" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="08e93c48-eb69-4e75-b7f3-976d4a8059ad" name="更改" comment="" />
      <created>1752717195965</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752717195965</updated>
      <workItem from="1752717198249" duration="23697000" />
      <workItem from="1752807785319" duration="17926000" />
      <workItem from="1753060167834" duration="52135000" />
      <workItem from="1753234323821" duration="2547000" />
      <workItem from="1753247808828" duration="20966000" />
      <workItem from="1753275611315" duration="22396000" />
      <workItem from="1753338662956" duration="16969000" />
      <workItem from="1753626549605" duration="3980000" />
      <workItem from="1753683336076" duration="812000" />
      <workItem from="1753721565099" duration="117000" />
      <workItem from="1753722013151" duration="241000" />
      <workItem from="1753723352089" duration="49000" />
      <workItem from="1753759682190" duration="403000" />
      <workItem from="1753760095021" duration="34000" />
      <workItem from="1753763620731" duration="64000" />
      <workItem from="1753763702337" duration="5799000" />
      <workItem from="1753810371123" duration="8452000" />
    </task>
    <task id="LOCAL-00001" summary="骨架完成">
      <option name="closed" value="true" />
      <created>1752717374745</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1752717374745</updated>
    </task>
    <task id="LOCAL-00002" summary="基础信息完成">
      <option name="closed" value="true" />
      <created>1752759405621</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1752759405621</updated>
    </task>
    <task id="LOCAL-00003" summary="缓存">
      <option name="closed" value="true" />
      <created>1752806820940</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1752806820940</updated>
    </task>
    <task id="LOCAL-00004" summary="所有基础代码完成">
      <option name="closed" value="true" />
      <created>1752812793046</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1752812793046</updated>
    </task>
    <task id="LOCAL-00005" summary="wx模块">
      <option name="closed" value="true" />
      <created>1753076021796</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1753076021796</updated>
    </task>
    <option name="localTasksCounter" value="6" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="骨架完成" />
    <MESSAGE value="基础信息完成" />
    <MESSAGE value="缓存" />
    <MESSAGE value="所有基础代码完成" />
    <MESSAGE value="wx模块" />
    <option name="LAST_COMMIT_MESSAGE" value="wx模块" />
    <option name="GROUP_MULTIFILE_MERGE_BY_DIRECTORY" value="true" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/wx/src/main/java/com/yt/wx/handler/ReceiveTextMsgHandler.java</url>
          <line>48</line>
          <option name="timeStamp" value="6" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/wx/src/main/java/com/yt/wx/handler/ReceiveTextMsgHandler.java</url>
          <line>41</line>
          <option name="timeStamp" value="7" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/wx/src/main/java/com/yt/wx/handler/ReceiveTextMsgHandler.java</url>
          <line>44</line>
          <option name="timeStamp" value="9" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/wx/src/main/java/com/yt/wx/handler/WxChatMsgTypeEnum.java</url>
          <line>22</line>
          <option name="timeStamp" value="11" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/wx/src/main/java/com/yt/wx/handler/WxChatMsgTypeEnum.java</url>
          <line>23</line>
          <option name="timeStamp" value="12" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/wx/src/main/java/com/yt/wx/controller/CallBackController.java</url>
          <line>85</line>
          <option name="timeStamp" value="14" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/wx/src/main/java/com/yt/wx/controller/CallBackController.java</url>
          <line>88</line>
          <option name="timeStamp" value="15" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/gateway/src/main/java/com/yt/gateway/filter/LoginFilter.java</url>
          <line>23</line>
          <option name="timeStamp" value="16" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/gateway/src/main/java/com/yt/gateway/filter/LoginFilter.java</url>
          <line>24</line>
          <option name="timeStamp" value="17" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/gateway/src/main/java/com/yt/gateway/filter/LoginFilter.java</url>
          <line>34</line>
          <option name="timeStamp" value="18" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/gateway/src/main/java/com/yt/gateway/filter/LoginFilter.java</url>
          <line>35</line>
          <option name="timeStamp" value="19" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/gateway/src/main/java/com/yt/gateway/filter/LoginFilter.java</url>
          <line>36</line>
          <option name="timeStamp" value="20" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/gateway/src/main/java/com/yt/gateway/filter/LoginFilter.java</url>
          <line>45</line>
          <option name="timeStamp" value="21" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/gateway/src/main/java/com/yt/gateway/filter/LoginFilter.java</url>
          <line>52</line>
          <option name="timeStamp" value="25" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/gateway/src/main/java/com/yt/gateway/filter/LoginFilter.java</url>
          <line>56</line>
          <option name="timeStamp" value="26" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/gateway/src/main/java/com/yt/gateway/filter/LoginFilter.java</url>
          <line>64</line>
          <option name="timeStamp" value="27" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/gateway/src/main/java/com/yt/gateway/filter/LoginFilter.java</url>
          <line>65</line>
          <option name="timeStamp" value="28" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/gateway/src/main/java/com/yt/gateway/filter/LoginFilter.java</url>
          <line>68</line>
          <option name="timeStamp" value="29" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/gateway/src/main/java/com/yt/gateway/filter/LoginFilter.java</url>
          <line>71</line>
          <option name="timeStamp" value="30" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/gateway/src/main/java/com/yt/gateway/filter/LoginFilter.java</url>
          <line>47</line>
          <option name="timeStamp" value="31" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/gateway/src/main/java/com/yt/gateway/filter/LoginFilter.java</url>
          <line>46</line>
          <option name="timeStamp" value="32" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <pin-to-top-manager>
      <pinned-members>
        <PinnedItemInfo parentTag="java.util.HashMap$Node" memberName="value" />
      </pinned-members>
    </pin-to-top-manager>
    <watches-manager>
      <configuration name="SpringBootApplicationConfigurationType">
        <watch expression="actual" />
      </configuration>
    </watches-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>