package com.yt.subject.domain.hander.subject;

import com.yt.subject.commom.enums.SubjectInfoEnums;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public class SubjectTypeHandlerFactory {

    @Resource
    private List<SubjectTypeHandler> subjectTypeHandlerList;

    private Map<SubjectInfoEnums, SubjectTypeHandler> handlerMap=new HashMap<>();

    public SubjectTypeHandler getSubjectTypeHandler(int subjectType) {
        SubjectInfoEnums subjectInfoEnums=SubjectInfoEnums.getByCode(subjectType);
        return handlerMap.get(subjectInfoEnums);
    }

    @PostConstruct
    public void init() {
        for (SubjectTypeHandler subjectTypeHandler : subjectTypeHandlerList) {
            handlerMap.put(subjectTypeHandler.getSubjectType(), subjectTypeHandler);
        }
    }
}
