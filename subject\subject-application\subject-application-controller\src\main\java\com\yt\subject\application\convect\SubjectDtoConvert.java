package com.yt.subject.application.convect;

import com.yt.subject.application.entity.SubjectInfoDto;
import com.yt.subject.domain.entity.SubjectInfoBo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface SubjectDtoConvert {
    SubjectDtoConvert INSTANCE = Mappers.getMapper(SubjectDtoConvert.class);
    SubjectInfoBo dtoToBo(SubjectInfoDto subjectDto);
    List<SubjectInfoDto> boToDto(List<SubjectInfoBo> subjectInfoBoList);

}
