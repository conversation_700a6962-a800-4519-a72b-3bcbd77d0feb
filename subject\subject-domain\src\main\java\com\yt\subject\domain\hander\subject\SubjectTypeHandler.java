package com.yt.subject.domain.hander.subject;

import com.yt.subject.commom.enums.SubjectInfoEnums;
import com.yt.subject.domain.entity.SubjectInfoBo;
import com.yt.subject.domain.entity.SubjectOptionBo;

/**
 * <AUTHOR>
 */
public interface SubjectTypeHandler {

    SubjectInfoEnums getSubjectType();

    Boolean add(SubjectInfoBo subjectInfoBo);

    /**
     * 实际的题目的插入
     */
    SubjectOptionBo query(int subjectId);
}
