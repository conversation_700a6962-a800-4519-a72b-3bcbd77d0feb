package com.yt.auth.domain.service;

import cn.dev33.satoken.stp.SaTokenInfo;
import com.yt.auth.domain.entity.AuthUserBo;

public interface AuthUserDomainService {

    Boolean delete(AuthUserBo authUserBo);

    Boolean update(AuthUserBo authUserBo);

    Boolean register(AuthUserBo authUserBo);

    Boolean updateId(AuthUserBo authUserBo);

    SaTokenInfo doLogin(String loginCode);

    AuthUserBo getUserInfo(AuthUserBo authUserBo);
}