package com.yt.auth.application.convect;

import com.yt.auth.application.entity.AuthRolePermissionDto;
import com.yt.auth.domain.entity.AuthRolePermissionBo;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-29T01:01:21+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
public class RolePermissionDtoConvertImpl implements RolePermissionDtoConvert {

    @Override
    public AuthRolePermissionBo dtoToBo(AuthRolePermissionDto authRolePermissionDto) {
        if ( authRolePermissionDto == null ) {
            return null;
        }

        AuthRolePermissionBo authRolePermissionBo = new AuthRolePermissionBo();

        authRolePermissionBo.setId( authRolePermissionDto.getId() );
        authRolePermissionBo.setRoleId( authRolePermissionDto.getRoleId() );
        authRolePermissionBo.setPermissionId( authRolePermissionDto.getPermissionId() );
        authRolePermissionBo.setIsDeleted( authRolePermissionDto.getIsDeleted() );
        List<Long> list = authRolePermissionDto.getPermissionIdList();
        if ( list != null ) {
            authRolePermissionBo.setPermissionIdList( new ArrayList<Long>( list ) );
        }

        return authRolePermissionBo;
    }
}
