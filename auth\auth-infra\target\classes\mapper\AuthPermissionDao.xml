<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yt.auth.infra.basic.dao.AuthPermissionDao">

    <resultMap type="com.yt.auth.infra.basic.entity.AuthPermission" id="AuthPermissionMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="parentId" column="parent_id" jdbcType="INTEGER"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="menuUrl" column="menu_url" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="show" column="show" jdbcType="INTEGER"/>
        <result property="icon" column="icon" jdbcType="VARCHAR"/>
        <result property="permissionKey" column="permission_key" jdbcType="VARCHAR"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
    </resultMap>

    <select id="queryById" resultMap="AuthPermissionMap">
        select
            id, name, parent_id, type, menu_url, status, `show`, icon, permission_key, created_by, created_time, update_by, update_time, is_deleted
        from auth_permission
        where id = #{id}
    </select>

    <select id="queryAllByLimit" resultMap="AuthPermissionMap">
        select
        id, name, parent_id, type, menu_url, status, `show`, icon, permission_key, created_by, created_time, update_by, update_time, is_deleted
        from
        auth_permission
        where
        id in
        <foreach collection="Ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and is_deleted = 0
    </select>

    <select id="count" resultType="java.lang.Long">
        select count(1)
        from auth_permission
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
            <if test="parentId != null">
                and parent_id = #{parentId}
            </if>
            <if test="type != null">
                and type = #{type}
            </if>
            <if test="menuUrl != null and menuUrl != ''">
                and menu_url = #{menuUrl}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="show != null">
                and `show` = #{show}
            </if>
            <if test="icon != null and icon != ''">
                and icon = #{icon}
            </if>
            <if test="permissionKey != null and permissionKey != ''">
                and permission_key = #{permissionKey}
            </if>
            <if test="createdBy != null and createdBy != ''">
                and created_by = #{createdBy}
            </if>
            <if test="createdTime != null">
                and created_time = #{createdTime}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and update_by = #{updateBy}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="isDeleted != null">
                and is_deleted = #{isDeleted}
            </if>
        </where>
    </select>

    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into auth_permission(name, parent_id, type, menu_url, status, `show`, icon, permission_key, created_by, created_time, update_by, update_time, is_deleted)
        values (#{name}, #{parentId}, #{type}, #{menuUrl}, #{status}, #{show}, #{icon}, #{permissionKey}, #{createdBy}, #{createdTime}, #{updateBy}, #{updateTime}, #{isDeleted})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into auth_permission(name, parent_id, type, menu_url, status, `show`, icon, permission_key, created_by, created_time, update_by, update_time, is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.name}, #{entity.parentId}, #{entity.type}, #{entity.menuUrl}, #{entity.status}, #{entity.show}, #{entity.icon}, #{entity.permissionKey}, #{entity.createdBy}, #{entity.createdTime}, #{entity.updateBy}, #{entity.updateTime}, #{entity.isDeleted})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into auth_permission(name, parent_id, type, menu_url, status, `show`, icon, permission_key, created_by, created_time, update_by, update_time, is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.name}, #{entity.parentId}, #{entity.type}, #{entity.menuUrl}, #{entity.status}, #{entity.show}, #{entity.icon}, #{entity.permissionKey}, #{entity.createdBy}, #{entity.createdTime}, #{entity.updateBy}, #{entity.updateTime}, #{entity.isDeleted})
        </foreach>
        on duplicate key update
        name = values(name),
        parent_id = values(parent_id),
        type = values(type),
        menu_url = values(menu_url),
        status = values(status),
        `show` = values(show),
        icon = values(icon),
        permission_key = values(permission_key),
        created_by = values(created_by),
        created_time = values(created_time),
        update_by = values(update_by),
        update_time = values(update_time),
        is_deleted = values(is_deleted)
    </insert>

    <update id="update">
        update auth_permission
        <set>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="parentId != null">
                parent_id = #{parentId},
            </if>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="menuUrl != null and menuUrl != ''">
                menu_url = #{menuUrl},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="show != null">
                `show` = #{show},
            </if>
            <if test="icon != null and icon != ''">
                icon = #{icon},
            </if>
            <if test="permissionKey != null and permissionKey != ''">
                permission_key = #{permissionKey},
            </if>
            <if test="createdBy != null and createdBy != ''">
                created_by = #{createdBy},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted},
            </if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteById">
        delete from auth_permission where id = #{id}
    </delete>

</mapper>