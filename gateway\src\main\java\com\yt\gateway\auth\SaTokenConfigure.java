package com.yt.gateway.auth;

import cn.dev33.satoken.context.SaHolder;
import cn.dev33.satoken.reactor.filter.SaReactorFilter;
import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.stp.StpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * [Sa-Token 权限认证] 配置类
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class SaTokenConfigure {
    // 注册 Sa-Token全局过滤器
    @Bean
    public SaReactorFilter getSaReactorFilter() {
        return new SaReactorFilter()
                // 拦截地址
                .addInclude("/**")
                // 鉴权方法：每次访问进入
                .setAuth(obj -> {
                    String requestPath = SaHolder.getRequest().getRequestPath();
                    log.info("=== SaTokenConfigure 权限验证开始 ===");
                    log.info("SaTokenConfigure.前端访问的path: {}", requestPath);

                    try {
                        // 检查当前登录状态
                        boolean isLogin = StpUtil.isLogin();
                        log.info("SaTokenConfigure.当前登录状态: {}", isLogin);

                        if (isLogin) {
                            Object loginId = StpUtil.getLoginId();
                            log.info("SaTokenConfigure.当前登录用户ID: {}", loginId);

                            // 获取用户角色列表
                            List<String> roleList = StpUtil.getRoleList();
                            log.info("SaTokenConfigure.用户角色列表: {}", roleList);

                            // 获取用户权限列表
                            List<String> permissionList = StpUtil.getPermissionList();
                            log.info("SaTokenConfigure.用户权限列表: {}", permissionList);
                        }

                        // 登录校验 -- 拦截所有路由，并排除/user/doLogin 用于开放登录
                        SaRouter.match("/auth/**")
                                .notMatch("/auth/user/doLogin")
                                .notMatch("/auth/user/doRegister")
                                .check(r -> {
                                    log.info("SaTokenConfigure.开始检查normal_user角色");
                                    try {
                                        StpUtil.checkRole("normal_user");
                                        log.info("SaTokenConfigure.normal_user角色验证通过");
                                    } catch (Exception e) {
                                        log.error("SaTokenConfigure.normal_user角色验证失败: {}", e.getMessage());
                                        throw e;
                                    }
                                });

                        // SaRouter.match("/oss/**",  r -> StpUtil.checkLogin()); // 临时注释，用于测试文件上传
                        SaRouter.match("/subject/**",  r -> {
                            log.info("SaTokenConfigure.开始检查subject模块登录状态");
                            StpUtil.checkLogin();
                            log.info("SaTokenConfigure.subject模块登录验证通过");
                        }).match("/subject/subject/add/",  r -> {
                            log.info("SaTokenConfigure.开始检查subject:add权限");
                            StpUtil.checkPermission("subject:add");
                            log.info("SaTokenConfigure.subject:add权限验证通过");
                        });

                        log.info("SaTokenConfigure.权限验证全部通过");

                    } catch (Exception e) {
                        log.error("SaTokenConfigure.权限验证失败: {}", e.getMessage(), e);
                        throw e;
                    }

                    log.info("=== SaTokenConfigure 权限验证结束 ===");
                });
    }
}
