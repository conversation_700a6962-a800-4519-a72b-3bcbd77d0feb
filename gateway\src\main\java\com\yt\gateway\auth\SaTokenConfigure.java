package com.yt.gateway.auth;

import cn.dev33.satoken.context.SaHolder;
import cn.dev33.satoken.reactor.filter.SaReactorFilter;
import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.stp.StpUtil;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * [Sa-Token 权限认证] 配置类
 * <AUTHOR>
 */
@Configuration
public class SaTokenConfigure {
    // 注册 Sa-Token全局过滤器
    @Bean
    public SaReactorFilter getSaReactorFilter() {
        return new SaReactorFilter()
                // 拦截地址
                .addInclude("/**")
                // 鉴权方法：每次访问进入
                .setAuth(obj -> {
                    System.out.println("--------- 前端访问的 path 是：" + SaHolder.getRequest().getRequestPath());
                    // 登录校验 -- 拦截所有路由，并排除/user/doLogin 用于开放登录
                    SaRouter.match("/auth/**")
                            .notMatch("/auth/user/doLogin")
                            .notMatch("/auth/user/doRegister")
                            .check(r -> StpUtil.checkRole("normal_user"));
                    // SaRouter.match("/oss/**",  r -> StpUtil.checkLogin()); // 临时注释，用于测试文件上传
                    SaRouter.match("/subject/**",  r -> StpUtil.checkLogin())
                            .match("/subject/subject/add/",  r -> StpUtil.checkPermission("subject:add"));
                });
    }
}
