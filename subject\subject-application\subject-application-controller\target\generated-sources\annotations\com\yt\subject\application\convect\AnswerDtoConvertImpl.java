package com.yt.subject.application.convect;

import com.yt.subject.application.entity.SubjectAnswerDto;
import com.yt.subject.domain.entity.SubjectAnswerBo;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-29T01:01:30+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
public class AnswerDtoConvertImpl implements AnswerDtoConvert {

    @Override
    public List<SubjectAnswerBo> dtoToBo(List<SubjectAnswerDto> subjectAnswerDtos) {
        if ( subjectAnswerDtos == null ) {
            return null;
        }

        List<SubjectAnswerBo> list = new ArrayList<SubjectAnswerBo>( subjectAnswerDtos.size() );
        for ( SubjectAnswerDto subjectAnswerDto : subjectAnswerDtos ) {
            list.add( subjectAnswerDtoToSubjectAnswerBo( subjectAnswerDto ) );
        }

        return list;
    }

    protected SubjectAnswerBo subjectAnswerDtoToSubjectAnswerBo(SubjectAnswerDto subjectAnswerDto) {
        if ( subjectAnswerDto == null ) {
            return null;
        }

        SubjectAnswerBo subjectAnswerBo = new SubjectAnswerBo();

        subjectAnswerBo.setOptionType( subjectAnswerDto.getOptionType() );
        subjectAnswerBo.setOptionContent( subjectAnswerDto.getOptionContent() );
        subjectAnswerBo.setIsCorrect( subjectAnswerDto.getIsCorrect() );

        return subjectAnswerBo;
    }
}
