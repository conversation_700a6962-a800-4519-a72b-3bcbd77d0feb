package com.yt.auth.domain.service.impl;

import cn.dev33.satoken.secure.SaSecureUtil;
import cn.dev33.satoken.stp.SaTokenInfo;
import cn.dev33.satoken.stp.StpUtil;
import com.google.gson.Gson;
import com.yt.auth.commom.enums.AuthUserStatusEnum;
import com.yt.auth.commom.enums.IsDeletedFlagEnum;
import com.yt.auth.commom.utils.RedisUtil;
import com.yt.auth.domain.constants.AuthConstants;
import com.yt.auth.domain.convert.UserBoConvert;
import com.yt.auth.domain.entity.AuthUserBo;
import com.yt.auth.domain.service.AuthUserDomainService;
import com.yt.auth.infra.basic.entity.*;
import com.yt.auth.infra.basic.service.*;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 */
@Service
public class AuthUserDomainServiceImpl implements AuthUserDomainService {

    // ==================== 核心改动 1: 注入自身的代理对象 ====================
    /**
     * 注入自身的代理对象，用于解决事务自调用失效问题。
     * 必须配合 @Lazy 注解，以打破Spring容器启动时产生的循环依赖。
     */
    @Resource
    @Lazy
    private AuthUserDomainService self;
    // =====================================================================

    @Resource
    private AuthUserService authUserService;

    @Resource
    private AuthUserRoleService authUserRoleService;

    @Resource
    private AuthRoleService authRoleService;

    @Resource
    private AuthRolePermissionService authRolePermissionService;

    @Resource
    private AuthPermissionService authPermissionService;

    @Resource
    private RedisUtil redisUtil;

    private final String authPermissionPrefix= "auth.Permission";
    private final String authRolePrefix= "auth.Role";
    private static final String LOGIN_PREFIX = "loginCode";

    /**
     * 注册
     * 此方法现在可以通过 self.register() 被外部代理调用，使得事务生效。
     */
    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public Boolean register(AuthUserBo authUserBo){
        AuthUser authUser = UserBoConvert.INSTANCE.boToy(authUserBo);
        //校验用户是否存在
        Boolean rs=authUserService.queryByUsername(authUser);
        if(rs) return true;
        if(StringUtils.isNotBlank(authUser.getPassword()))authUser.setPassword(SaSecureUtil.md5BySalt(authUser.getPassword(), "wys17740473227"));
        authUser.setStatus(AuthUserStatusEnum.OPEN.getCode());
        authUser.setIsDeleted(IsDeletedFlagEnum.UN_DELETED.getCode());
        authUserService.insert(authUser);

        //角色和用户进行关联
        AuthRole authRole = new AuthRole();
        authRole.setRoleKey(AuthConstants.NORMAL_USER);
        authRole.setIsDeleted(IsDeletedFlagEnum.UN_DELETED.getCode());
        AuthRole roleRs = authRoleService.queryByCondition(authRole);

        // 如果未找到角色，则抛出异常或进行处理
        if (roleRs == null) {
            throw new Exception("未找到'normal_user'角色配置，请检查数据库！");
        }

        Long roleId = roleRs.getId();
        Long userId = authUser.getId();
        AuthUserRole authUserRole = new AuthUserRole();
        authUserRole.setRoleId(roleId);
        authUserRole.setUserId(userId);
        authUserRole.setIsDeleted(IsDeletedFlagEnum.UN_DELETED.getCode());

        String roleKey = redisUtil.buildKey(authRolePrefix, authUser.getUserName());
        List<AuthRole> roles = new ArrayList<>();
        roles.add(roleRs);
        redisUtil.set(roleKey, new Gson().toJson(roles));

        AuthRolePermission authRolePermission = new AuthRolePermission();
        authRolePermission.setRoleId(roleId);

        //根据roleId查权限
        List<AuthRolePermission> authRolePermissions = authRolePermissionService.queryByCondition(authRolePermission);
        List<Long> permissionIds = authRolePermissions.stream().map(AuthRolePermission::getPermissionId).collect(Collectors.toList());
        List<AuthPermission> permissions = Collections.emptyList();
        if (!permissionIds.isEmpty()) {
            permissions = authPermissionService.queryByPermissionList(permissionIds);
        }

        String permissionKey = redisUtil.buildKey(authPermissionPrefix, authUser.getUserName());
        redisUtil.set(permissionKey, new Gson().toJson(permissions));
        return authUserRoleService.insert(authUserRole);
    }

    /**
     * 登录方法
     */
    @Override
    public SaTokenInfo doLogin(String loginCode) {
        String key = redisUtil.buildKey(LOGIN_PREFIX, loginCode);
        String openId = redisUtil.get(key);
        if (StringUtils.isBlank(openId)) {
            return null;
        }
        AuthUserBo authUserBo = new AuthUserBo();
        authUserBo.setUserName(openId);
        self.register(authUserBo);
        String roleKey = redisUtil.buildKey(authRolePrefix, openId);
        if (!redisUtil.exist(roleKey)) {
            AuthUser queryUser = new AuthUser();
            queryUser.setUserName(openId);
            AuthUser dbUser = authUserService.queryBy(queryUser);
            if (dbUser != null) {
                AuthUserRole userRoleQuery = new AuthUserRole();
                userRoleQuery.setUserId(dbUser.getId());
                List<AuthUserRole> userRoles = authUserRoleService.queryByCondition(userRoleQuery);
                List<Long> roleIds = userRoles.stream().map(AuthUserRole::getRoleId).collect(Collectors.toList());

                List<AuthRole> roles = Collections.emptyList();
                if (!roleIds.isEmpty()) {
                    roles = authRoleService.queryByIds(roleIds);
                }
                redisUtil.set(roleKey, new Gson().toJson(roles));

                List<AuthPermission> permissions = Collections.emptyList();
                if (!roleIds.isEmpty()) {
                    List<AuthRolePermission> rolePermissions = authRolePermissionService.queryByRoleIds(roleIds);
                    List<Long> permissionIds = rolePermissions.stream()
                            .map(AuthRolePermission::getPermissionId)
                            .distinct()
                            .collect(Collectors.toList());

                    if (!permissionIds.isEmpty()) {
                        permissions = authPermissionService.queryByPermissionList(permissionIds);
                    }
                }
                String permissionKey = redisUtil.buildKey(authPermissionPrefix, openId);
                redisUtil.set(permissionKey, new Gson().toJson(permissions));
            }
        }

        StpUtil.login(openId);
        return StpUtil.getTokenInfo();
    }

    @Override
    public Boolean updateId(AuthUserBo authUserBo) {
        AuthUser authUser = UserBoConvert.INSTANCE.boToy(authUserBo);
        return authUserService.updateId(authUser);
    }

    @Override
    public AuthUserBo getUserInfo(AuthUserBo authUserBo) {
        AuthUser authUser = UserBoConvert.INSTANCE.boToy(authUserBo);
        AuthUser user = authUserService.queryBy(authUser);
        return UserBoConvert.INSTANCE.yToBo(user);
    }

    @Override
    public Boolean update(AuthUserBo authUserBo) {
        AuthUser authUser = UserBoConvert.INSTANCE.boToy(authUserBo);
        return authUserService.update(authUser);
    }

    @Override
    public Boolean delete(AuthUserBo authUserBo) {
        AuthUser authUser = new AuthUser();
        authUser.setId(authUserBo.getId());
        authUser.setIsDeleted(IsDeletedFlagEnum.DELETED.getCode());
        return authUserService.updateId(authUser);
    }
}