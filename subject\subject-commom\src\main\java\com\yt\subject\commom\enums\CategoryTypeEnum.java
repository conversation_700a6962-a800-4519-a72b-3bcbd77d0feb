package com.yt.subject.commom.enums;

import lombok.Getter;

/**
 * 分类类型枚举
 * <AUTHOR>
 */
@Getter
public enum CategoryTypeEnum {

    PRIMARY(1, "岗位大类"),
    SECOND(2, "二级分类");

    // 将字段修改为 private final
    private final int code;
    private final String desc;

    CategoryTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static CategoryTypeEnum getByCode(int codeVal) {
        for (CategoryTypeEnum categoryEnum : CategoryTypeEnum.values()) {
            if (categoryEnum.code == codeVal) {
                return categoryEnum;
            }
        }
        return null;
    }

}