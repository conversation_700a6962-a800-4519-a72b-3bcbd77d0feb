package com.yt.auth.infra.basic.service.impl;

import com.yt.auth.infra.basic.entity.AuthRole;
import com.yt.auth.infra.basic.dao.AuthRoleDao;
import com.yt.auth.infra.basic.service.AuthRoleService;
import org.springframework.stereotype.Service;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import javax.annotation.Resource;
import java.util.List;

/**
 * (AuthRole)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-17 17:04:41
 */
@Service("authRoleService")
public class AuthRoleServiceImpl implements AuthRoleService {
    @Resource
    private AuthRoleDao authRoleDao;

    /**
     * 通过ID查询单条数据
     */
    @Override
    public AuthRole queryById(Long id) {
        return this.authRoleDao.queryById(id);
    }

    /**
     * 分页查询
     */
    @Override
    public Page<AuthRole> queryByPage(AuthRole authRole, PageRequest pageRequest) {
        long total = this.authRoleDao.count(authRole);
        return new PageImpl<>(this.authRoleDao.queryAllByLimit(authRole, pageRequest), pageRequest, total);
    }

    /**
     * 新增数据
     */
    @Override
    public Boolean insert(AuthRole authRole) {
        return authRoleDao.insert(authRole)>0;
    }

    /**
     * 修改数据
     */
    @Override
    public Boolean update(AuthRole authRole) {
        return authRoleDao.update(authRole)>0;
    }

    /**
     * 通过主键删除数据
     */
    @Override
    public boolean deleteById(Long id) {
        return this.authRoleDao.deleteById(id) > 0;
    }

    @Override
    public AuthRole queryByCondition(AuthRole authRole) {
        return  authRoleDao.queryByCondition(authRole);
    }

    @Override
    public List<AuthRole> queryByIds(List<Long> roleIds) {
        return  authRoleDao.queryByIds(roleIds);
    }
}
