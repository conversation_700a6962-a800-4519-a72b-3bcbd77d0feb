package com.yt.subject.domain.convert;

import com.yt.subject.domain.entity.SubjectAnswerBo;
import com.yt.subject.infra.basic.entity.SubjectMultiple;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-29T01:01:28+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
public class MultipleConverterImpl implements MultipleConverter {

    @Override
    public SubjectMultiple convertBoToEntity(SubjectAnswerBo subjectAnswerBO) {
        if ( subjectAnswerBO == null ) {
            return null;
        }

        SubjectMultiple subjectMultiple = new SubjectMultiple();

        subjectMultiple.setOptionType( subjectAnswerBO.getOptionType() );
        subjectMultiple.setOptionContent( subjectAnswerBO.getOptionContent() );
        subjectMultiple.setIsCorrect( subjectAnswerBO.getIsCorrect() );

        return subjectMultiple;
    }

    @Override
    public List<SubjectAnswerBo> convertEntityToBoList(List<SubjectMultiple> subjectMultipleList) {
        if ( subjectMultipleList == null ) {
            return null;
        }

        List<SubjectAnswerBo> list = new ArrayList<SubjectAnswerBo>( subjectMultipleList.size() );
        for ( SubjectMultiple subjectMultiple : subjectMultipleList ) {
            list.add( subjectMultipleToSubjectAnswerBo( subjectMultiple ) );
        }

        return list;
    }

    protected SubjectAnswerBo subjectMultipleToSubjectAnswerBo(SubjectMultiple subjectMultiple) {
        if ( subjectMultiple == null ) {
            return null;
        }

        SubjectAnswerBo subjectAnswerBo = new SubjectAnswerBo();

        subjectAnswerBo.setOptionType( subjectMultiple.getOptionType() );
        subjectAnswerBo.setOptionContent( subjectMultiple.getOptionContent() );
        subjectAnswerBo.setIsCorrect( subjectMultiple.getIsCorrect() );

        return subjectAnswerBo;
    }
}
