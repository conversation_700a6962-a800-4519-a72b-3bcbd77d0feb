package com.yt.auth.application.entity;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * (AuthPermission)实体类
 *
 * <AUTHOR>
 * @since 2025-07-17 17:04:41
 */
@Data
public class AuthPermissionDto implements Serializable {
    @Serial
    private static final long serialVersionUID = 365780981138804462L;

    private Long id;
/**
     * 权限名称
     */
    private String name;
/**
     * 父id
     */
    private Long parentId;
/**
     * 权限类型 0菜单 1操作
     */
    private Integer type;
/**
     * 菜单路由
     */
    private String menuUrl;
/**
     * 状态 0启用 1禁用
     */
    private Integer status;
/**
     * 展示状态 0展示 1隐藏
     */
    private Integer show;
/**
     * 图标
     */
    private String icon;
/**
     * 权限唯一标识
     */
    private String permissionKey;

/**
     * 是否被删除 0为删除 1已删除
     */
    private Integer isDeleted;
}

