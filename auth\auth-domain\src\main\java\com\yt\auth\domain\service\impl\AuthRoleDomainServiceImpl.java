package com.yt.auth.domain.service.impl;

import com.yt.auth.commom.enums.IsDeletedFlagEnum;
import com.yt.auth.domain.convert.RoleBoConvert;
import com.yt.auth.domain.entity.AuthRoleBo;
import com.yt.auth.domain.service.AuthRoleDomainService;
import com.yt.auth.infra.basic.entity.AuthRole;
import com.yt.auth.infra.basic.service.AuthRoleService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.Objects;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
public class AuthRoleDomainServiceImpl implements AuthRoleDomainService {

    @Resource
    private AuthRoleService authRoleService;

    @Override
    @Transactional
    public Boolean add(AuthRoleBo authRoleBo) {
        // 【修复】在插入前，先检查具有相同roleKey的角色是否已经存在
        AuthRole existingRole = new AuthRole();
        existingRole.setRoleKey(authRoleBo.getRoleKey());
        AuthRole roleInDb = authRoleService.queryByCondition(existingRole);

        // 如果数据库中已存在同名的角色，则直接返回true，表示操作成功（实现幂等性）
        if (Objects.nonNull(roleInDb)) {
            return true;
        }

        AuthRole authRole = RoleBoConvert.INSTANCE.boToy(authRoleBo);
        authRole.setIsDeleted(IsDeletedFlagEnum.UN_DELETED.getCode());
        return authRoleService.insert(authRole);
    }

    @Override
    public Boolean update(AuthRoleBo authRoleBo) {
        AuthRole authRole = RoleBoConvert.INSTANCE.boToy(authRoleBo);
        return authRoleService.update(authRole);
    }

    @Override
    public Boolean delete(AuthRoleBo authRoleBo) {
        AuthRole authRole = new AuthRole();
        authRole.setId(authRoleBo.getId());
        authRole.setIsDeleted(IsDeletedFlagEnum.DELETED.getCode());
        return authRoleService.update(authRole);
    }

}