package com.yt.subject.application.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Preconditions;
import com.yt.subject.application.convect.CategoryDtoConvert;
import com.yt.subject.application.convect.LabelDtoConvert;
import com.yt.subject.application.entity.SubjectCategoryDto;
import com.yt.subject.application.entity.SubjectLabelDto;
import com.yt.subject.commom.entity.Result;
import com.yt.subject.domain.entity.SubjectCategoryBo;
import com.yt.subject.domain.service.SubjectCategoryDomainService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/subject/category")
@Slf4j
public class SubjectCategoryController{

    @Resource
    private SubjectCategoryDomainService subjectCategoryDomainservice;

    @RequestMapping("/add")
    public Result<Object> add(@RequestBody SubjectCategoryDto subjectCategoryDto) {
        try{
            if(log.isDebugEnabled()){
            log.info("SubjectCategoryController.add.subjectCategoryDto:{}", JSON.toJSONString(subjectCategoryDto));
            }
            Preconditions.checkNotNull(subjectCategoryDto.getCategoryType(),"分类类型不能为空");
            Preconditions.checkArgument(!StringUtils.isBlank(subjectCategoryDto.getCategoryName()), "分类名称不能为空");
            Preconditions.checkNotNull(subjectCategoryDto.getParentId(),"分类父级id不能为空");
            SubjectCategoryBo subjectCategoryBo = CategoryDtoConvert.INSTANCE.dtoToBo(subjectCategoryDto);
            subjectCategoryDomainservice.add(subjectCategoryBo);
            return Result.success(true);
            }catch (Exception e){
            log.error("SubjectCategoryController.add.error:{}", e.getMessage(), e);
            return Result.error("新增分类失败");
        }
    }

    /**
     * 查询岗位大类
     */
    @PostMapping("/queryPrimaryCategory")
    public Result<List<SubjectCategoryDto>> queryPrimaryCategory(@RequestBody SubjectCategoryDto subjectCategoryDto) {
        try {
            if(log.isDebugEnabled()){
                log.info("SubjectCategoryController.queryPrimaryCategory.subjectCategoryDto:{}", JSON.toJSONString(subjectCategoryDto));
            }
            SubjectCategoryBo subjectCategoryBO = CategoryDtoConvert.INSTANCE.dtoToBo(subjectCategoryDto);
            List<SubjectCategoryBo> boList = subjectCategoryDomainservice.queryCategory(subjectCategoryBO);
            List<SubjectCategoryDto> dtoList = CategoryDtoConvert.INSTANCE.boToDtoList(boList);
            return Result.success(dtoList);
        } catch (Exception e) {
            log.error("SubjectCategoryController.queryPrimaryCategory.error:{}", e.getMessage(), e);
            return Result.error("查询失败");
        }

    }

    /**
     * 根据分类id查二级分类
     */
    @PostMapping("/queryCategoryByPrimary")
    public Result<List<SubjectCategoryDto>> queryCategoryByPrimary(@RequestBody SubjectCategoryDto subjectCategoryDTO) {
        try {
            if (log.isInfoEnabled()) {
                log.info("SubjectCategoryController.queryCategoryByPrimary.subjectCategoryDto:{}", JSON.toJSONString(subjectCategoryDTO));
            }
            Preconditions.checkNotNull(subjectCategoryDTO.getParentId(), "分类id不能为空");
            SubjectCategoryBo subjectCategoryBO = CategoryDtoConvert.INSTANCE.dtoToBo(subjectCategoryDTO);
            List<SubjectCategoryBo> boList = subjectCategoryDomainservice.queryCategory(subjectCategoryBO);
            List<SubjectCategoryDto> dtoList = CategoryDtoConvert.INSTANCE.boToDtoList(boList);
            return Result.success(dtoList);
        } catch (Exception e) {
            log.error("SubjectCategoryController.queryCategoryByPrimary.error:{}", e.getMessage(), e);
            return Result.error("查询失败");
        }
    }

    @RequestMapping("/update")
    public Result<Object> update(@RequestBody SubjectCategoryDto subjectCategoryDto) {
        try{
            if(log.isDebugEnabled()){
                log.info("SubjectCategoryController.update.subjectCategoryDto:{}", JSON.toJSONString(subjectCategoryDto));
            }
            Preconditions.checkNotNull(subjectCategoryDto.getId(),"分类id不能为空");
            SubjectCategoryBo subjectCategoryBo = CategoryDtoConvert.INSTANCE.dtoToBo(subjectCategoryDto);
            Boolean b = subjectCategoryDomainservice.update(subjectCategoryBo);
            return Result.success(b);
        }catch (Exception e){
            log.error("SubjectCategoryController.update.error:{}", e.getMessage(), e);
            return Result.error("更新分类失败");
        }
    }

    @RequestMapping("/delete")
    public Result<Object> delete(@RequestBody SubjectCategoryDto subjectCategoryDto) {
        try{
            if(log.isDebugEnabled()){
                log.info("SubjectCategoryController.delete.subjectCategoryDto:{}", JSON.toJSONString(subjectCategoryDto));
            }
            Preconditions.checkNotNull(subjectCategoryDto.getId(),"分类id不能为空");
            SubjectCategoryBo subjectCategoryBo = CategoryDtoConvert.INSTANCE.dtoToBo(subjectCategoryDto);
            Boolean b = subjectCategoryDomainservice.delete(subjectCategoryBo);
            return Result.success(b);
        }catch (Exception e){
            log.error("SubjectCategoryController.delete.error:{}", e.getMessage(), e);
            return Result.error("删除分类失败");
        }
    }

    /**
     * 查询分类及标签一次性
     */
    @PostMapping("/queryCategoryAndLabel")
    public Result<List<SubjectCategoryDto>> queryCategoryAndLabel(@RequestBody SubjectCategoryDto subjectCategoryDTO) {
        try {
            if (log.isInfoEnabled()) {
                log.info("SubjectCategoryController.queryCategoryAndLabel.dto:{}"
                        , JSON.toJSONString(subjectCategoryDTO));
            }
            Preconditions.checkNotNull(subjectCategoryDTO.getId(), "分类id不能为空");
            SubjectCategoryBo subjectCategoryBO = CategoryDtoConvert.INSTANCE.dtoToBo(subjectCategoryDTO);
            List<SubjectCategoryBo> subjectCategoryBOList = subjectCategoryDomainservice.queryCategoryAndLabel(subjectCategoryBO);
            List<SubjectCategoryDto> dtoList = new LinkedList<>();
            subjectCategoryBOList.forEach(bo -> {
                SubjectCategoryDto dto = CategoryDtoConvert.INSTANCE.boToDto(bo);
                List<SubjectLabelDto> labelDTOList = LabelDtoConvert.INSTANCE.boToDto(bo.getLabelBOList());
                dto.setLabelDTOList(labelDTOList);
                dtoList.add(dto);
            });
            return Result.success(dtoList);
        } catch (Exception e) {
            log.error("SubjectCategoryController.queryPrimaryCategory.error:{}", e.getMessage(), e);
            return Result.error("查询失败");
        }
    }

}
