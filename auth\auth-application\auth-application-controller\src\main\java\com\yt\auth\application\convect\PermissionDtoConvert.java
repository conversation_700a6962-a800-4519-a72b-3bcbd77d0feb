package com.yt.auth.application.convect;


import com.yt.auth.application.entity.AuthPermissionDto;
import com.yt.auth.domain.entity.AuthPermissionBo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface PermissionDtoConvert {
    PermissionDtoConvert INSTANCE = Mappers.getMapper(PermissionDtoConvert.class);
    AuthPermissionBo dtoToBo(AuthPermissionDto authPermissionDto);
    List<AuthPermissionDto> boToDto(List<AuthPermissionBo> authPermissionBo);
}
