<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ExternalStorageConfigurationManager" enabled="true" />
  <component name="MavenProjectsManager">
    <option name="originalFiles">
      <list>
        <option value="$PROJECT_DIR$/subject/subject-infra/pom.xml" />
        <option value="$PROJECT_DIR$/subject/pom.xml" />
        <option value="$PROJECT_DIR$/subject/subject-commom/pom.xml" />
        <option value="$PROJECT_DIR$/oss/pom.xml" />
        <option value="$PROJECT_DIR$/auto/pom.xml" />
        <option value="$PROJECT_DIR$/gateway/pom.xml" />
        <option value="$PROJECT_DIR$/auth/pom.xml" />
        <option value="$PROJECT_DIR$/auth/auth-application/auth-application-controller/pom.xml" />
        <option value="$PROJECT_DIR$/wx/pom.xml" />
      </list>
    </option>
    <option name="ignoredFiles">
      <set>
        <option value="$PROJECT_DIR$/auth/auth-application-job/pom.xml" />
        <option value="$PROJECT_DIR$/auto/pom.xml" />
      </set>
    </option>
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_17" project-jdk-name="ms-17" project-jdk-type="JavaSDK">
    <output url="file://$PROJECT_DIR$/out" />
  </component>
</project>