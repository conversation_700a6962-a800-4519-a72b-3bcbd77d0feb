package com.yt.subject.domain.service.serviceImpl;

import com.alibaba.fastjson.JSON;
import com.yt.subject.commom.enums.IsDeletedFlagEnum;
import com.yt.subject.domain.convert.CategoryBoConvert;
import com.yt.subject.domain.entity.SubjectCategoryBo;
import com.yt.subject.domain.entity.SubjectLabelBo;
import com.yt.subject.domain.service.SubjectCategoryDomainService;
import com.yt.subject.domain.utils.CacheUtil;
import com.yt.subject.infra.basic.entity.SubjectCategory;
import com.yt.subject.infra.basic.entity.SubjectLabel;
import com.yt.subject.infra.basic.entity.SubjectMapping;
import com.yt.subject.infra.basic.service.SubjectCategoryService;
import com.yt.subject.infra.basic.service.SubjectLabelService;
import com.yt.subject.infra.basic.service.SubjectMappingService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SubjectCategoryDomainDServiceImpl implements SubjectCategoryDomainService {

    @Resource
    private SubjectLabelService subjectLabelService;

    @Resource
    private SubjectMappingService subjectMappingService;

    @Resource
    private SubjectCategoryService subjectCategoryService;

    @Resource
    private CacheUtil cacheUtil;

    @Resource
    private ThreadPoolExecutor labelThreadPool;

    @Override
    public void add(SubjectCategoryBo subjectCategoryBo) {
        SubjectCategory subjectCategory = CategoryBoConvert.INSTANCE.boToY(subjectCategoryBo);
        subjectCategory.setIsDeleted(IsDeletedFlagEnum.UN_DELETED.getCode());
        subjectCategoryService.insert(subjectCategory);
    }

    @Override
    public List<SubjectCategoryBo> queryCategory(SubjectCategoryBo subjectCategoryBo) {

        SubjectCategory subjectCategory = CategoryBoConvert.INSTANCE.boToY(subjectCategoryBo);
        subjectCategory.setIsDeleted(IsDeletedFlagEnum.UN_DELETED.getCode());
        List<SubjectCategory> yList=subjectCategoryService.queryCategory(subjectCategory);
        List<SubjectCategoryBo> boList=CategoryBoConvert.INSTANCE.yToBo(yList);
        if (log.isInfoEnabled()){
            log.info("SubjectCategoryController.queryCategory.boList:{}", JSON.toJSONString(boList));
        }
        boList.forEach(bo->{
            Integer count=subjectCategoryService.queryCount(bo.getId());
          bo.setCount(count);
        });
        return boList;
    }

    @Override
    public Boolean update(SubjectCategoryBo subjectCategoryBo) {
        SubjectCategory subjectCategory = CategoryBoConvert.INSTANCE.boToY(subjectCategoryBo);
       return subjectCategoryService.update(subjectCategory);
    }

    @Override
    public Boolean delete(SubjectCategoryBo subjectCategoryBo) {
        SubjectCategory subjectCategory = CategoryBoConvert.INSTANCE.boToY(subjectCategoryBo);
        subjectCategory.setIsDeleted(IsDeletedFlagEnum.DELETED.getCode());
        return subjectCategoryService.update(subjectCategory);
    }

    @SneakyThrows
    @Override
    public List<SubjectCategoryBo> queryCategoryAndLabel(SubjectCategoryBo subjectCategoryBO) {
        Long id = subjectCategoryBO.getId();
        String cacheKey = "categoryAndLabel." + subjectCategoryBO.getId();
        List<SubjectCategoryBo> subjectCategoryBos = cacheUtil.getResult(cacheKey,
                SubjectCategoryBo.class, (key) -> getSubjectCategoryBOS(id));
        return subjectCategoryBos;
    }

    private List<SubjectCategoryBo> getSubjectCategoryBOS(Long categoryId) {
        SubjectCategory subjectCategory = new SubjectCategory();
        subjectCategory.setParentId(categoryId);
        subjectCategory.setIsDeleted(IsDeletedFlagEnum.UN_DELETED.getCode());
        List<SubjectCategory> subjectCategoryList = subjectCategoryService.queryCategory(subjectCategory);
        if (log.isInfoEnabled()) {
            log.info("SubjectCategoryController.queryCategoryAndLabel.subjectCategoryList:{}",
                    JSON.toJSONString(subjectCategoryList));
        }
        List<SubjectCategoryBo> categoryBOList = CategoryBoConvert.INSTANCE.yToBo(subjectCategoryList);
        Map<Long, List<SubjectLabelBo>> map = new HashMap<>();
        List<CompletableFuture<Map<Long, List<SubjectLabelBo>>>> completableFutureList = categoryBOList.stream().map(category ->
                CompletableFuture.supplyAsync(() -> getLabelBOList(category), labelThreadPool)
        ).collect(Collectors.toList());
        completableFutureList.forEach(future -> {
            try {
                Map<Long, List<SubjectLabelBo>> resultMap = future.get();
                if (!MapUtils.isEmpty(resultMap)) {
                    map.putAll(resultMap);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        categoryBOList.forEach(categoryBO -> {
            if (!CollectionUtils.isEmpty(map.get(categoryBO.getId()))) {
                categoryBO.setLabelBOList(map.get(categoryBO.getId()));
            }
        });
        return categoryBOList;
    }

    private Map<Long, List<SubjectLabelBo>> getLabelBOList(SubjectCategoryBo category) {
        if (log.isInfoEnabled()) {
            log.info("getLabelBOList:{}", JSON.toJSONString(category));
        }
        Map<Long, List<SubjectLabelBo>> labelMap = new HashMap<>();
        SubjectMapping subjectMapping = new SubjectMapping();
        subjectMapping.setCategoryId(category.getId());
        List<SubjectMapping> mappingList = subjectMappingService.queryLabelId(subjectMapping);
        if (CollectionUtils.isEmpty(mappingList)) {
            return null;
        }
        List<Long> labelIdList = mappingList.stream().map(SubjectMapping::getLabelId).collect(Collectors.toList());
        List<SubjectLabel> labelList = subjectLabelService.batchQueryById(labelIdList);
        List<SubjectLabelBo> labelBOList = new LinkedList<>();
        labelList.forEach(label -> {
            SubjectLabelBo subjectLabelBO = new SubjectLabelBo();
            subjectLabelBO.setId(label.getId());
            subjectLabelBO.setLabelName(label.getLabelName());
            subjectLabelBO.setCategoryId(label.getCategoryId());
            subjectLabelBO.setSortNum(label.getSortNum());
            labelBOList.add(subjectLabelBO);
        });
        labelMap.put(category.getId(), labelBOList);
        return labelMap;
    }

}
