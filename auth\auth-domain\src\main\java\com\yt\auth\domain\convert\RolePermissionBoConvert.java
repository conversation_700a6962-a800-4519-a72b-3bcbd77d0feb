package com.yt.auth.domain.convert;


import com.yt.auth.domain.entity.AuthRolePermissionBo;
import com.yt.auth.infra.basic.entity.AuthRolePermission;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface RolePermissionBoConvert {
    RolePermissionBoConvert INSTANCE = Mappers.getMapper(RolePermissionBoConvert.class);
    AuthRolePermission boToy(AuthRolePermissionBo authRolePermissionBo);
}
