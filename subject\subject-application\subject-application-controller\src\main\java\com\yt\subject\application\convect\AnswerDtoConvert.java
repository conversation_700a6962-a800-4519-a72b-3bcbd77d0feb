package com.yt.subject.application.convect;

import com.yt.subject.application.entity.SubjectAnswerDto;
import com.yt.subject.domain.entity.SubjectAnswerBo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface AnswerDtoConvert {
    AnswerDtoConvert INSTANCE = Mappers.getMapper(AnswerDtoConvert.class);
    List<SubjectAnswerBo> dtoToBo(List<SubjectAnswerDto> subjectAnswerDtos);
}
