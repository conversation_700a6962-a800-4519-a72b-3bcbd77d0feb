package com.yt.subject.domain.convert;

import com.yt.subject.domain.entity.SubjectCategoryBo;
import com.yt.subject.infra.basic.entity.SubjectCategory;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface CategoryBoConvert {
    CategoryBoConvert INSTANCE = Mappers.getMapper(CategoryBoConvert.class);
    SubjectCategory boToY(SubjectCategoryBo subjectCategory);
    List<SubjectCategoryBo> yToBo(List<SubjectCategory> subjectCategory);
    List<SubjectCategory> boToyList(List<SubjectCategoryBo> subjectCategory);
}
