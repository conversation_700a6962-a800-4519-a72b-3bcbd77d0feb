
package com.yt.auth.application.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Preconditions;
import com.yt.auth.application.convect.RolePermissionDtoConvert;
import com.yt.auth.application.entity.AuthRolePermissionDto;

import com.yt.auth.domain.entity.AuthRolePermissionBo;
import com.yt.auth.domain.service.AuthRolePermissionDomainService;
import com.yt.auth.entity.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/rolePermission")
@Slf4j
public class RolePermissionController {

    @Resource
    private AuthRolePermissionDomainService authRolePermissionDomainService;


    /**
     *  新增权限
     */
    @RequestMapping("/add")
    public Result<Boolean> add(@RequestBody AuthRolePermissionDto authRolePermissionDto){
        try {
            if(log.isDebugEnabled()) {
                log.info("RolePermissionController.add.controller:{}", JSON.toJSONString(authRolePermissionDto));
            }
            Preconditions.checkArgument(!authRolePermissionDto.getPermissionIdList().isEmpty(), "关联权限不为空");
            Preconditions.checkNotNull(authRolePermissionDto.getRoleId(),"角色id不为空");
            AuthRolePermissionBo authedRolePermissionBo = RolePermissionDtoConvert.INSTANCE.dtoToBo(authRolePermissionDto);
            return Result.success(authRolePermissionDomainService.add(authedRolePermissionBo));
        }catch (Exception e){
            log.error("RolePermissionController.add.error:{}", e.getMessage(),e);
            return Result.error("新增权限失败");
        }
    }
}
