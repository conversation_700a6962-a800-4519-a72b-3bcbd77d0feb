package com.yt.wx.handler;

import com.yt.wx.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ReceiveTextMsgHandler implements WxChatMsgHandler {

    private static final String KEY_WORD = "验证码";
    private static final String LOGIN_PREFIX = "loginCode";
    // 新增一个前缀，用于存储已处理的 MsgId
    private static final String MSG_ID_PREFIX = "msgId";

    @Resource
    private RedisUtil redisUtil;

    @Override
    public WxChatMsgTypeEnum getMsgType() {
        return WxChatMsgTypeEnum.TEXT_MSG;
    }

    /**
     * 处理文本消息，如果内容是“验证码”，则生成验证码并返回要回复的文本。
     * @param messageMap 接收到的消息
     * @return 需要回复给用户的文本内容，如果无需回复则返回空字符串。
     */
    @Override
    public String dealMsg(Map<String, String> messageMap) {
        String msgId = messageMap.get("MsgId");
        String content = messageMap.get("Content");
        // 1. 使用 MsgId 进行请求去重
        String msgIdKey = redisUtil.buildKey(MSG_ID_PREFIX, msgId);
        // 尝试将 msgId 存入 Redis，设置60秒过期
        if (!redisUtil.setNx(msgIdKey, "1", 60L, TimeUnit.SECONDS)) {
            log.warn("检测到重复消息，MsgId: {}", msgId);
            return "";
        }
        log.info("接收到文本消息事件，MsgId: {}", msgId);
        if (!KEY_WORD.equals(content)) {
            return "";
        }

        // --- 生成验证码的业务逻辑 ---
        String fromUserName = messageMap.get("FromUserName");

        Random random = new Random();
        int num = random.nextInt(899) + 100;
        String numKey = redisUtil.buildKey(LOGIN_PREFIX, String.valueOf(num));
        redisUtil.setNx(numKey, fromUserName, 5L, TimeUnit.MINUTES);

        // 直接返回要发送给用户的验证码文本
        return "您当前的验证码是：" + num + "！ 5分钟内有效";
    }
}