package com.yt.subject.domain.convert;


import com.yt.subject.domain.entity.SubjectAnswerBo;
import com.yt.subject.infra.basic.entity.SubjectRadio;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface RadioConverter {

    RadioConverter INSTANCE = Mappers.getMapper(RadioConverter.class);

    SubjectRadio convertBoToEntity(SubjectAnswerBo subjectAnswerBO);

    List<SubjectAnswerBo> convertEntityToBoList(List<SubjectRadio> subjectRadioList);

}
