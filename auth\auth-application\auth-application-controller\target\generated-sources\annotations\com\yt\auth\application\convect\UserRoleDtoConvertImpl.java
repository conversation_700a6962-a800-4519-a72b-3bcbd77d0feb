package com.yt.auth.application.convect;

import com.yt.auth.application.entity.AuthUserRoleDto;
import com.yt.auth.domain.entity.AuthUserRoleBo;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-29T01:01:21+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
public class UserRoleDtoConvertImpl implements UserRoleDtoConvert {

    @Override
    public AuthUserRoleBo dtoToBo(AuthUserRoleDto authUserRoleDto) {
        if ( authUserRoleDto == null ) {
            return null;
        }

        AuthUserRoleBo authUserRoleBo = new AuthUserRoleBo();

        authUserRoleBo.setId( authUserRoleDto.getId() );
        authUserRoleBo.setUserId( authUserRoleDto.getUserId() );
        authUserRoleBo.setRoleId( authUserRoleDto.getRoleId() );
        authUserRoleBo.setIsDeleted( authUserRoleDto.getIsDeleted() );

        return authUserRoleBo;
    }
}
