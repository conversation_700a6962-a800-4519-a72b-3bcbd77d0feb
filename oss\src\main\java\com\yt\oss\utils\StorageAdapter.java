package com.yt.oss.utils;

import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface StorageAdapter {

    /**
     * 文件预览
     */
    String getPresignedObjectUrl(String bucketName, String objectName);

    /**
     * 文件下载
     */
    byte[] downloadFileAsBytes(String fileName, String bucketName);

    /**
     * 列出所有存储桶的存储桶信息
     */
    List<String> listBuckets();

    /**
     * 文件上传
     */
    String uploadFile(MultipartFile uploadFile,String bucket,String objectName);

}
