package com.yt.auth.infra.basic.service.impl;

import com.yt.auth.infra.basic.entity.AuthUser;
import com.yt.auth.infra.basic.dao.AuthUserDao;
import com.yt.auth.infra.basic.service.AuthUserService;
import org.springframework.stereotype.Service;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import javax.annotation.Resource;

/**
 * 用户信息表(AuthUser)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-17 17:04:41
 */
@Service("authUserService")
public class AuthUserServiceImpl implements AuthUserService {
    @Resource
    private AuthUserDao authUserDao;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public AuthUser queryById(Long id) {
        return this.authUserDao.queryById(id);
    }

    @Override
    public AuthUser queryBy(AuthUser authUser) {
        return this.authUserDao.queryAllByLimit(authUser);
    }


    /**
     * 新增数据
     *
     * @param authUser 实例对象
     * @return 实例对象
     */
    @Override
    public Integer insert(AuthUser authUser) {
        return authUserDao.insert(authUser);
    }

    /**
     * 修改数据
     *
     * @param authUser 实例对象
     * @return 实例对象
     */
    @Override
    public Boolean update(AuthUser authUser) {
        return authUserDao.update(authUser)>0;
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(Long id) {
        return this.authUserDao.deleteById(id) > 0;
    }

    @Override
    public Boolean updateId(AuthUser authUser) {
        return authUserDao.updateId(authUser)>0;
    }

    @Override
    public Boolean queryByUsername(AuthUser authUser) {
        return authUserDao.queryByUsername(authUser)>0;
    }
}
