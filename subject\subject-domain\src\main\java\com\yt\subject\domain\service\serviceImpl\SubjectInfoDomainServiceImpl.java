package com.yt.subject.domain.service.serviceImpl;

import com.alibaba.fastjson.JSON;
import com.yt.subject.commom.entity.PageResult;
import com.yt.subject.commom.enums.IsDeletedFlagEnum;
import com.yt.subject.domain.convert.SubjectBoConvert;
import com.yt.subject.domain.entity.SubjectInfoBo;
import com.yt.subject.domain.entity.SubjectOptionBo;
import com.yt.subject.domain.hander.subject.SubjectTypeHandler;
import com.yt.subject.domain.hander.subject.SubjectTypeHandlerFactory;
import com.yt.subject.domain.service.SubjectInfoDomainService;
import com.yt.subject.infra.basic.entity.SubjectInfo;
import com.yt.subject.infra.basic.entity.SubjectLabel;
import com.yt.subject.infra.basic.entity.SubjectMapping;
import com.yt.subject.infra.basic.service.SubjectInfoService;
import com.yt.subject.infra.basic.service.SubjectLabelService;
import com.yt.subject.infra.basic.service.SubjectMappingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SubjectInfoDomainServiceImpl implements SubjectInfoDomainService {

    @Resource
    private SubjectInfoService subjectInfoService;

    @Resource
    private SubjectMappingService subjectMappingService;

    @Resource
    private SubjectTypeHandlerFactory subjectTypeHandlerFactory;

    @Resource
    private SubjectLabelService subjectLabelService;

    @Override
    public Boolean add(SubjectInfoBo subjectInfoBo){
        if(log.isDebugEnabled()){
            log.info("SubjectInfoDomainServiceImpl.add.subjectInfoBo:{}", JSON.toJSONString(subjectInfoBo));
        }
        SubjectInfo subjectInfo = SubjectBoConvert.INSTANCE.boToY(subjectInfoBo);
        subjectInfoService.insert(subjectInfo);
        // 将生成的ID设置回BO对象
        subjectInfoBo.setId(subjectInfo.getId());
        SubjectTypeHandler subjectTypeHandler = subjectTypeHandlerFactory.getSubjectTypeHandler(subjectInfo.getSubjectType());
        subjectTypeHandler.add(subjectInfoBo);
        List<Integer> categoryIds = subjectInfoBo.getCategoryIds();
        List<Integer> labelIds = subjectInfoBo.getLabelIds();
        List<SubjectMapping> mappingList=new LinkedList<>();
        categoryIds.forEach(categoryId-> labelIds.forEach(labelId->{
            SubjectMapping mapping = new SubjectMapping();
            mapping.setSubjectId(subjectInfo.getId());
            mapping.setCategoryId(Long.valueOf(categoryId));
            mapping.setLabelId(Long.valueOf(labelId));
            mappingList.add(mapping);
        }));
        return subjectMappingService.batchAdd(mappingList);
    }

    @Override
    public PageResult<SubjectInfoBo> getSubjectPage(SubjectInfoBo subjectInfoBo) {
        PageResult<SubjectInfoBo> pageResult=new PageResult<>();
        pageResult.setPageNo(subjectInfoBo.getPageNo());
        pageResult.setPageSize(subjectInfoBo.getPageSize());
        int start=(subjectInfoBo.getPageNo()-1)*subjectInfoBo.getPageSize();
        SubjectInfo subjectInfo =SubjectBoConvert.INSTANCE.boToY(subjectInfoBo);
        int count=subjectInfoService.countInfo(subjectInfo,subjectInfoBo.getCategoryId(),subjectInfoBo.getLabelId());
        if (count == 0) {
            return pageResult;
        }
        List<SubjectInfo> subjectInfoList = subjectInfoService.queryByPage(subjectInfo, subjectInfoBo.getCategoryId()
                , subjectInfoBo.getLabelId(), start, subjectInfoBo.getPageSize());
        List<SubjectInfoBo> subjectInfoBos = SubjectBoConvert.INSTANCE.yToBo(subjectInfoList);
        subjectInfoBos.forEach(info -> {
            SubjectMapping subjectMapping = new SubjectMapping();
            subjectMapping.setSubjectId(info.getId());
            List<SubjectMapping> mappingList = subjectMappingService.queryLabelId(subjectMapping);
            List<Long> labelIds = mappingList.stream().map(SubjectMapping::getLabelId).collect(Collectors.toList());
            List<SubjectLabel> labelList = subjectLabelService.batchQueryById(labelIds);
            List<String> labelNames = labelList.stream().map(SubjectLabel::getLabelName).collect(Collectors.toList());
            info.setLabelName(labelNames);
        });
        pageResult.setRecords(subjectInfoBos);
        pageResult.setTotal(count);
        return pageResult;
    }

    @Override
    public SubjectInfoBo querySubjectInfo(SubjectInfoBo subjectInfoBo) {
        //查询当前题目的题目信息
        SubjectInfo subjectInfo =subjectInfoService.queryById(subjectInfoBo.getId());
        SubjectTypeHandler subjectTypeHandler = subjectTypeHandlerFactory.getSubjectTypeHandler(subjectInfo.getSubjectType());
        //查询当前题目的具体答案
        SubjectOptionBo optionBO = subjectTypeHandler.query(subjectInfo.getId().intValue());
        SubjectInfoBo bo = SubjectBoConvert.INSTANCE.convertOptionAndInfoToBo(optionBO, subjectInfo);
        //查询当前题目所关联的标签id
        SubjectMapping subjectMapping = new SubjectMapping();
        subjectMapping.setSubjectId(subjectInfo.getId());
        subjectMapping.setIsDeleted(IsDeletedFlagEnum.UN_DELETED.getCode());
        //获取到所有关联的标签的id
        List<SubjectMapping> subjectMappings = subjectMappingService.queryLabelId(subjectMapping);
        List<Long> labelIdList = subjectMappings.stream().map(SubjectMapping::getLabelId).collect(Collectors.toList());
        //根据id查询所有关联的标签的名称
        List<SubjectLabel> labels = subjectLabelService.batchQueryById(labelIdList);
        List<String> labelNames = labels.stream().map(SubjectLabel::getLabelName).collect(Collectors.toList());
        //把所有的关联的标签名称导入到bo中做整合
        bo.setLabelName(labelNames);
        return bo;
    }
}